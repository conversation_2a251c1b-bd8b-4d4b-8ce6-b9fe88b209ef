{"name": "work-finder", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.3.0", "@ant-design/pro-components": "^2.6.49", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/styles": "^6.4.12", "@reduxjs/toolkit": "^2.2.1", "@tabler/icons-react": "^3.34.0", "antd": "^5.15.0", "axios": "^1.6.7", "axios-mock-adapter": "^1.22.0", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "file-saver": "^2.0.5", "framer-motion": "^11.0.8", "lodash": "^4.17.21", "lucide-react": "^0.523.0", "moment": "^2.30.1", "qs": "^6.11.2", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-beautiful-dnd-grid": "^0.1.3-alpha", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-intl": "^6.6.2", "react-multi-email": "^1.0.15", "react-number-format": "^5.3.1", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^9.1.0", "react-router-dom": "^6.22.2", "react-slick": "^0.30.2", "react-timer-hook": "^3.0.7", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "tailwind-merge": "^3.3.1", "yup": "^1.3.3", "zustand": "^5.0.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.19", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "^5.2.2", "vite": "^5.1.4"}}