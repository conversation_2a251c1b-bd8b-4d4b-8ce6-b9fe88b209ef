import { ReactNode } from "react";

// Clone types từ vsii-internal-fe
export type OverrideIcon =
  | React.ComponentClass<any>
  | React.FunctionComponent<any>;

export type LinkTarget = "_blank" | "_self" | "_parent" | "_top";

export type NavItemType = {
  id?: string;
  icon?: OverrideIcon;
  target?: boolean;
  external?: boolean;
  url?: string | undefined;
  defaultUrl?: boolean;
  type?: string;
  title?: ReactNode | string;
  color?: "primary" | "secondary" | "default" | undefined;
  caption?: ReactNode | string;
  breadcrumbs?: boolean;
  disabled?: boolean;
  chip?: any;
  children?: NavItemType[];
  elements?: NavItemType[];
  search?: string;
  access?: string[];
};

// Menu slice state - clone từ vsii-internal-fe
export type MenuProps = {
  selectedItem: string[];
  selectedID: string | null;
  drawerOpen: boolean;
};
