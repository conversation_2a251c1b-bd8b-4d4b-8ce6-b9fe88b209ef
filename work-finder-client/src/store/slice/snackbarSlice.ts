import { createSlice } from "@reduxjs/toolkit";
import type { RootState } from "../../app/store";

interface SnackbarState {
  open: boolean;
  message: string;
  variant: string;
  alert: {
    color: string;
  };
  transition?: string;
  close?: boolean;
  actionButton?: boolean;
}

const initialState: SnackbarState = {
  open: false,
  message: "",
  variant: "alert",
  alert: {
    color: "info",
  },
  transition: "SlideUp",
  close: true,
  actionButton: false,
};

const snackbarSlice = createSlice({
  name: "snackbar",
  initialState,
  reducers: {
    openSnackbar: (state, action) => {
      state.open = action.payload.open || true;
      state.message = action.payload.message;
      state.variant = action.payload.variant || "alert";
      state.alert = action.payload.alert || { color: "info" };
      state.transition = action.payload.transition || "SlideUp";
      state.close = action.payload.close === false ? false : true;
      state.actionButton = action.payload.actionButton || false;
    },
    closeSnackbar: (state) => {
      state.open = false;
    },
  },
});

export const { openSnackbar, closeSnackbar } = snackbarSlice.actions;

export const snackbarSelector = (state: RootState) => state.snackbar;

export default snackbarSlice.reducer;
