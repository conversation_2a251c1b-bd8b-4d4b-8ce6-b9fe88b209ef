import { createSlice } from "@reduxjs/toolkit";
import type { RootState } from "../../app/store";

interface DeniedPermissionState {
  show: boolean;
  isTabWrap: boolean;
}

const initialState: DeniedPermissionState = {
  show: false,
  isTabWrap: false,
};

const deniedPermissionSlice = createSlice({
  name: "deniedPermission",
  initialState,
  reducers: {
    openDeniedPermission: (state) => {
      state.show = true;
    },
    closeDeniedPermission: (state) => {
      state.show = false;
    },
    setIsTabWrap: (state, action) => {
      state.isTabWrap = action.payload;
    },
  },
});

export const { openDeniedPermission, closeDeniedPermission, setIsTabWrap } =
  deniedPermissionSlice.actions;

export const deniedPermissionSelector = (state: RootState) =>
  state.deniedPermission;

export default deniedPermissionSlice.reducer;
