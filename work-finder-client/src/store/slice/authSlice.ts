import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import type { RootState } from "../../app/store";

interface AuthState {
  isLoggedIn: boolean;
  user: any | null;
  loading: Record<string, boolean>;
  showLoadingScreen: boolean;
  isAccountExpired: boolean;
}

const initialState: AuthState = {
  isLoggedIn: false,
  user: null,
  loading: {},
  showLoadingScreen: true,
  isAccountExpired: false,
};

export const getUserInfo = createAsyncThunk(
  "auth/getUserInfo",
  async (params: any, { rejectWithValue }) => {
    try {
      // API call will be implemented here
      return { user: { name: "Test User" } };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isLoggedIn = true;
    },
    logout: (state) => {
      state.user = null;
      state.isLoggedIn = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getUserInfo.pending, (state) => {
        state.loading[getUserInfo.typePrefix] = true;
      })
      .addCase(getUserInfo.fulfilled, (state, action) => {
        state.loading[getUserInfo.typePrefix] = false;
        state.user = action.payload.user;
        state.isLoggedIn = true;
        state.showLoadingScreen = false;
      })
      .addCase(getUserInfo.rejected, (state) => {
        state.loading[getUserInfo.typePrefix] = false;
        state.showLoadingScreen = false;
      });
  },
});

export const { setUser, logout } = authSlice.actions;

export const authSelector = (state: RootState) => state.auth;

export default authSlice.reducer;
