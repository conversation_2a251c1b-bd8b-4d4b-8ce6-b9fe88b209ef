import { combineReducers } from "@reduxjs/toolkit";
import authReducer from "./authSlice";
import deniedPermissionReducer from "./deniedPermissionSlice";
import snackbarReducer from "./snackbarSlice";
import menuReducer from "./menuSlice";

// Slices will be imported here as needed
// Example: import authReducer from './authSlice';

const rootReducer = combineReducers({
  auth: authReducer,
  deniedPermission: deniedPermissionReducer,
  snackbar: snackbarReducer,
  menu: menuReducer,
  // Add more reducers here as needed
});

export default rootReducer;
