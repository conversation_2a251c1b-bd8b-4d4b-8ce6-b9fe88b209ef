// icons
import {
  HomeOutlined,
  SearchOutlined,
  BankOutlined,
  UserOutlined,
  EditOutlined,
  FileOutlined,
} from "@ant-design/icons";

// project import
import { NavItemType } from "@/types/menu";

// constant
const icons = {
  HomeOutlined,
  SearchOutlined,
  BankOutlined,
  UserOutlined,
  EditOutlined,
  FileOutlined,
};

// ==============================|| PUBLIC NAVBAR MENU ITEMS ||============================== //

const publicMenu: { items: NavItemType[] } = {
  items: [
    {
      id: "home",
      title: "Home",
      type: "collapse",
      icon: icons.HomeOutlined,
    },
    {
      id: "find-jobs",
      title: "Find Jobs",
      type: "collapse",
      icon: icons.SearchOutlined,
      children: [
        {
          id: "job-list",
          title: "Job List",
          type: "item",
          url: "/jobs",
          breadcrumbs: true,
        },
        {
          id: "job-grid",
          title: "Job Grid",
          type: "item",
          url: "/jobs/grid",
          breadcrumbs: true,
        },
        {
          id: "job-details",
          title: "Job Details",
          type: "item",
          url: "/jobs/details",
          breadcrumbs: true,
        },
        {
          id: "post-job",
          title: "Post a Job",
          type: "item",
          url: "/jobs/post",
          breadcrumbs: true,
        },
      ],
    },
    {
      id: "employers",
      title: "Employers",
      type: "collapse",
      icon: icons.BankOutlined,
      children: [
        {
          id: "employer-list",
          title: "Employer List",
          type: "item",
          url: "/employers",
          breadcrumbs: true,
        },
        {
          id: "employer-grid",
          title: "Employer Grid",
          type: "item",
          url: "/employers/grid",
          breadcrumbs: true,
        },
        {
          id: "employer-profile",
          title: "Employer Profile",
          type: "item",
          url: "/employers/profile",
          breadcrumbs: true,
        },
        {
          id: "employer-dashboard",
          title: "Employer Dashboard",
          type: "item",
          url: "/employers/dashboard",
          breadcrumbs: true,
        },
      ],
    },
    {
      id: "candidates",
      title: "Candidates",
      type: "collapse",
      icon: icons.UserOutlined,
      children: [
        {
          id: "candidate-list",
          title: "Candidate List",
          type: "item",
          url: "/candidates",
          breadcrumbs: true,
        },
        {
          id: "candidate-grid",
          title: "Candidate Grid",
          type: "item",
          url: "/candidates/grid",
          breadcrumbs: true,
        },
        {
          id: "candidate-profile",
          title: "Candidate Profile",
          type: "item",
          url: "/candidates/profile",
          breadcrumbs: true,
        },
        {
          id: "candidate-dashboard",
          title: "Candidate Dashboard",
          type: "item",
          url: "/candidates/dashboard",
          breadcrumbs: true,
        },
      ],
    },
    {
      id: "blog",
      title: "Blog",
      type: "collapse",
      icon: icons.EditOutlined,
      children: [
        {
          id: "blog-list",
          title: "Blog List",
          type: "item",
          url: "/blog",
          breadcrumbs: true,
        },
        {
          id: "blog-grid",
          title: "Blog Grid",
          type: "item",
          url: "/blog/grid",
          breadcrumbs: true,
        },
        {
          id: "blog-details",
          title: "Blog Details",
          type: "item",
          url: "/blog/details",
          breadcrumbs: true,
        },
        {
          id: "create-post",
          title: "Create Post",
          type: "item",
          url: "/blog/create",
          breadcrumbs: true,
        },
      ],
    },
    {
      id: "pages",
      title: "Pages",
      type: "collapse",
      icon: icons.FileOutlined,
      children: [
        {
          id: "about",
          title: "About Us",
          type: "item",
          url: "/about",
          breadcrumbs: true,
        },
        {
          id: "contact",
          title: "Contact",
          type: "item",
          url: "/contact",
          breadcrumbs: true,
        },
        {
          id: "pricing",
          title: "Pricing",
          type: "item",
          url: "/pricing",
          breadcrumbs: true,
        },
        {
          id: "faq",
          title: "FAQ",
          type: "item",
          url: "/faq",
          breadcrumbs: true,
        },
        {
          id: "privacy",
          title: "Privacy Policy",
          type: "item",
          url: "/privacy",
          breadcrumbs: true,
        },
        {
          id: "terms",
          title: "Terms of Service",
          type: "item",
          url: "/terms",
          breadcrumbs: true,
        },
      ],
    },
  ],
};

export default publicMenu;
