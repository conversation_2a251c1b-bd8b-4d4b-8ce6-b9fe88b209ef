// icons
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
} from "@ant-design/icons";

// project import
import { NavItemType } from "@/types/menu";

// constant
const icons = {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
};

const dashboard: NavItemType = {
  id: "dashboard-group",
  title: "Dashboard",
  type: "group",
  children: [
    {
      id: "dashboard",
      title: "Dashboard",
      type: "item",
      url: "/dashboard",
      icon: icons.DashboardOutlined,
      breadcrumbs: false,
    },
    {
      id: "users",
      title: "Users",
      type: "collapse",
      icon: icons.UserOutlined,
      children: [
        {
          id: "user-list",
          title: "User List",
          type: "item",
          url: "/users/list",
          breadcrumbs: true,
        },
        {
          id: "user-create",
          title: "Create User",
          type: "item",
          url: "/users/create",
          breadcrumbs: true,
        },
      ],
    },
    {
      id: "settings",
      title: "Settings",
      type: "item",
      url: "/settings",
      icon: icons.SettingOutlined,
      breadcrumbs: false,
    },
  ],
};

export default dashboard;
