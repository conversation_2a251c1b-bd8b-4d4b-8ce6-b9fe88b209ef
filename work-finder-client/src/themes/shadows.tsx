import { ColorProps } from "./palette";

export interface CustomShadowProps {
  z1: string;
  z8: string;
  z12: string;
  z16: string;
  z20: string;
  z24: string;
  primary: string;
  secondary: string;
  orange: string;
  success: string;
  warning: string;
  error: string;
}

/**
 * Generate shadow configuration for Ant Design theme
 */
const createCustomShadow = (
  theme: ColorProps,
  color: string
): CustomShadowProps => {
  const transparent = color + "3d"; // 24% opacity

  return {
    z1: `0 1px 2px 0 ${transparent}`,
    z8: `0 8px 16px 0 ${transparent}`,
    z12: `0 12px 24px 0 ${transparent} 0 10px 20px 0 ${transparent}`,
    z16: `0 0 3px 0 ${transparent} 0 14px 28px -5px ${transparent}`,
    z20: `0 0 3px 0 ${transparent} 0 18px 36px -5px ${transparent}`,
    z24: `0 0 6px 0 ${transparent} 0 21px 44px 0 ${transparent}`,

    primary: `0px 12px 14px 0px ${theme.primary}4d`, // 30% opacity
    secondary: `0px 12px 14px 0px ${theme.secondary}4d`,
    orange: `0px 12px 14px 0px ${theme.orangeMain}4d`,
    success: `0px 12px 14px 0px ${theme.success}4d`,
    warning: `0px 12px 14px 0px ${theme.warning}4d`,
    error: `0px 12px 14px 0px ${theme.error}4d`,
  };
};

/**
 * Generate shadow configuration based on theme mode
 */
const generateShadows = (
  navType: "light" | "dark",
  theme: ColorProps
): CustomShadowProps => {
  return navType === "dark"
    ? createCustomShadow(theme, theme.darkLevel1)
    : createCustomShadow(theme, theme.grey700);
};

export default generateShadows;
