import { useMemo, ReactNode } from "react";
import { ConfigProvider, theme as antTheme } from "antd";
import useConfig from "@/hooks/useConfig";
import generateTheme from "@/themes/palette";
import generateTypography from "@/themes/typography";
import generateShadows from "@/themes/shadows";

interface ThemeCustomizationProps {
  children: ReactNode;
}

/**
 * Theme Customization component that provides Ant Design theme configuration
 */
const ThemeCustomization = ({ children }: ThemeCustomizationProps) => {
  const { borderRadius, fontFamily, navType, outlinedFilled, presetColor } =
    useConfig();

  // Generate color palette based on configuration
  const colorPalette = useMemo(
    () => generateTheme(navType as "light" | "dark", presetColor),
    [navType, presetColor]
  );

  // Generate typography settings
  const typography = useMemo(
    () => generateTypography(colorPalette, borderRadius, fontFamily),
    [colorPalette, borderRadius, fontFamily]
  );

  // Generate shadow settings
  const shadows = useMemo(
    () => generateShadows(navType as "light" | "dark", colorPalette),
    [navType, colorPalette]
  );

  // Combine all theme settings into one theme object
  const themeConfig = useMemo(() => {
    // Create the Ant Design theme configuration
    const isDark = navType === "dark";

    return {
      token: {
        colorPrimary: colorPalette.primary,
        colorSuccess: colorPalette.success,
        colorWarning: colorPalette.warning,
        colorError: colorPalette.error,
        colorInfo: colorPalette.secondary,

        borderRadius: borderRadius,
        fontFamily: fontFamily,

        // Additional custom tokens
        colorBgContainer: isDark ? colorPalette.darkPaper : colorPalette.paper,
        colorTextBase: isDark
          ? colorPalette.darkTextPrimary
          : colorPalette.grey700,
        colorTextSecondary: isDark
          ? colorPalette.darkTextSecondary
          : colorPalette.grey500,

        // Custom shadows
        boxShadow: shadows.z1,
        boxShadowSecondary: shadows.z8,
      },
      algorithm: isDark ? antTheme.darkAlgorithm : antTheme.defaultAlgorithm,
      components: {
        Typography: typography,
        Button: {
          borderRadius: borderRadius,
          fontWeight: 500,
        },
        Card: {
          borderRadius: borderRadius,
        },
        Menu: {
          itemColor: colorPalette.grey700,
          itemHoverColor: colorPalette.primary,
          itemSelectedColor: colorPalette.primary,
          itemActiveBg: "transparent",
          itemHoverBg: "transparent",
          horizontalItemHoverColor: colorPalette.primary,
          horizontalItemSelectedColor: colorPalette.primary,
          horizontalItemHoverBg: "transparent",
          horizontalItemSelectedBg: "transparent",
        },
        // Add more component styles as needed
      },
    };
  }, [colorPalette, borderRadius, fontFamily, navType, shadows, typography]);

  return <ConfigProvider theme={themeConfig}>{children}</ConfigProvider>;
};

export default ThemeCustomization;
