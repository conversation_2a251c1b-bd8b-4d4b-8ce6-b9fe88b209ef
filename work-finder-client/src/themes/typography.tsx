import { ColorProps } from "./palette";

interface TypographyOptions {
  fontFamily: string;
  h1: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  h2: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  h3: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  h4: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  h5: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  h6: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  body1: {
    fontSize: string;
    fontWeight: number;
    lineHeight: string;
  };
  body2: {
    fontSize: string;
    fontWeight: number;
    lineHeight: string;
    color?: string;
  };
  subtitle1: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  subtitle2: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  caption: {
    fontSize: string;
    fontWeight: number;
    color?: string;
  };
  button: {
    textTransform: string;
  };
}

/**
 * Generate typography configuration for Ant Design theme
 */
const generateTypography = (
  theme: ColorProps,
  borderRadius: number,
  fontFamily: string
): TypographyOptions => {
  const isDark = theme.darkPaper === theme.paper;

  return {
    fontFamily,
    h1: {
      fontSize: "2.125rem",
      fontWeight: 700,
      color: isDark ? theme.darkTextPrimary : theme.grey700,
    },
    h2: {
      fontSize: "1.5rem",
      fontWeight: 700,
      color: isDark ? theme.darkTextPrimary : theme.grey500,
    },
    h3: {
      fontSize: "1rem",
      fontWeight: 600,
      color: isDark ? theme.darkTextPrimary : theme.grey500,
    },
    h4: {
      fontSize: "1rem",
      fontWeight: 600,
      color: isDark ? theme.darkTextPrimary : theme.grey500,
    },
    h5: {
      fontSize: "0.75rem",
      fontWeight: 500,
      color: isDark ? theme.darkTextPrimary : theme.grey500,
    },
    h6: {
      fontSize: "0.75rem",
      fontWeight: 500,
      color: isDark ? theme.darkTextPrimary : theme.grey500,
    },
    subtitle1: {
      fontSize: "0.75rem",
      fontWeight: 500,
      color: isDark ? theme.darkTextPrimary : theme.grey700,
    },
    subtitle2: {
      fontSize: "0.75rem",
      fontWeight: 400,
      color: isDark ? theme.darkTextSecondary : theme.grey500,
    },
    body1: {
      fontSize: "0.75rem",
      fontWeight: 400,
      lineHeight: "1.334em",
    },
    body2: {
      fontSize: "0.75rem",
      fontWeight: 400,
      lineHeight: "1.5em",
      color: isDark ? theme.darkTextPrimary : theme.grey700,
    },
    caption: {
      fontSize: "0.75rem",
      fontWeight: 400,
      color: isDark ? theme.darkTextSecondary : theme.grey500,
    },
    button: {
      textTransform: "capitalize",
    },
  };
};

export default generateTypography;
