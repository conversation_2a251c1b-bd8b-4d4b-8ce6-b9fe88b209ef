// Types
export interface ColorProps {
  // Common colors
  paper: string;
  darkPaper: string;

  // Primary colors
  primaryLight: string;
  primaryMain: string;
  primaryDark: string;
  primary200: string;
  primary800: string;

  // Secondary colors
  secondaryLight: string;
  secondaryMain: string;
  secondaryDark: string;
  secondary200: string;
  secondary800: string;

  // Dark mode primary colors
  darkPrimaryLight: string;
  darkPrimaryMain: string;
  darkPrimaryDark: string;
  darkPrimary200: string;
  darkPrimary800: string;

  // Dark mode secondary colors
  darkSecondaryLight: string;
  darkSecondaryMain: string;
  darkSecondaryDark: string;
  darkSecondary200: string;
  darkSecondary800: string;

  // Error colors
  errorLight: string;
  errorMain: string;
  errorDark: string;

  // Warning colors
  warningLight: string;
  warningMain: string;
  warningDark: string;

  // Success colors
  successLight: string;
  success200: string;
  successMain: string;
  successDark: string;

  // Orange colors
  orangeLight: string;
  orangeMain: string;
  orangeDark: string;

  // Grey colors
  grey50: string;
  grey100: string;
  grey200: string;
  grey300: string;
  grey500: string;
  grey700: string;
  grey900: string;

  // Dark mode text colors
  darkTextPrimary: string;
  darkTextSecondary: string;
  darkTextTitle: string;

  // Dark mode background colors
  darkLevel1: string;
  darkLevel2: string;
  darkBackground: string;

  // For Ant Design specific usage
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
}

// Default color palette
const defaultColors: ColorProps = {
  // Common
  paper: "#ffffff",
  darkPaper: "#121212",

  // Primary
  primaryLight: "#e3f2fd",
  primaryMain: "#1890ff",
  primaryDark: "#0c53b7",
  primary200: "#90caf9",
  primary800: "#1565c0",

  // Secondary
  secondaryLight: "#ede7f6",
  secondaryMain: "#673ab7",
  secondaryDark: "#5e35b1",
  secondary200: "#b39ddb",
  secondary800: "#4527a0",

  // Dark mode primary
  darkPrimaryLight: "#e3f2fd",
  darkPrimaryMain: "#1890ff",
  darkPrimaryDark: "#0c53b7",
  darkPrimary200: "#90caf9",
  darkPrimary800: "#1565c0",

  // Dark mode secondary
  darkSecondaryLight: "#d1c4e9",
  darkSecondaryMain: "#7c4dff",
  darkSecondaryDark: "#651fff",
  darkSecondary200: "#b39ddb",
  darkSecondary800: "#6200ea",

  // Error
  errorLight: "#ef9a9a",
  errorMain: "#f44336",
  errorDark: "#c62828",

  // Warning
  warningLight: "#fff8e1",
  warningMain: "#faad14",
  warningDark: "#ffc107",

  // Success
  successLight: "#b9f6ca",
  success200: "#69f0ae",
  successMain: "#52c41a",
  successDark: "#388e3c",

  // Orange
  orangeLight: "#fbe9e7",
  orangeMain: "#fa8c16",
  orangeDark: "#d84315",

  // Grey
  grey50: "#fafafa",
  grey100: "#f5f5f5",
  grey200: "#eeeeee",
  grey300: "#e0e0e0",
  grey500: "#9e9e9e",
  grey700: "#616161",
  grey900: "#212121",

  // Dark mode text
  darkTextPrimary: "#ffffff",
  darkTextSecondary: "#adb0bb",
  darkTextTitle: "#d7dcec",

  // Dark mode background
  darkLevel1: "#1a223f",
  darkLevel2: "#121926",
  darkBackground: "#111936",

  // For Ant Design
  primary: "#1890ff",
  secondary: "#673ab7",
  success: "#52c41a",
  warning: "#faad14",
  error: "#f44336",
};

// Theme color variations
const theme1 = {
  ...defaultColors,
  primaryMain: "#1890ff",
  primary: "#1890ff",
};

const theme2 = {
  ...defaultColors,
  primaryMain: "#00acc1",
  primary: "#00acc1",
};

const theme3 = {
  ...defaultColors,
  primaryMain: "#673ab7",
  primary: "#673ab7",
};

const theme4 = {
  ...defaultColors,
  primaryMain: "#8bc34a",
  primary: "#8bc34a",
};

const theme5 = {
  ...defaultColors,
  primaryMain: "#e91e63",
  primary: "#e91e63",
};

const theme6 = {
  ...defaultColors,
  primaryMain: "#ff9800",
  primary: "#ff9800",
};

// ==============================|| DEFAULT THEME - PALETTE  ||============================== //

const generateTheme = (
  navType: "light" | "dark",
  presetColor: string
): ColorProps => {
  let colors: ColorProps;

  switch (presetColor) {
    case "theme1":
      colors = theme1;
      break;
    case "theme2":
      colors = theme2;
      break;
    case "theme3":
      colors = theme3;
      break;
    case "theme4":
      colors = theme4;
      break;
    case "theme5":
      colors = theme5;
      break;
    case "theme6":
      colors = theme6;
      break;
    case "default":
    default:
      colors = defaultColors;
  }

  return colors;
};

export default generateTheme;
