import { lazy } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

// Project imports
import MainLayout from "./layout/MainLayout";
import DashboardLayout from "./layout/DashboardLayout";
import PublicLayout from "./layout/PublicLayout";
import AuthLayout from "./layout/AuthLayout";
import Loadable from "./components/Loadable";

// Route guards
import AuthGuard from "./utils/route-guard/AuthGuard";
import GuestGuard from "./utils/route-guard/GuestGuard";
import NotFound from "./utils/route-guard/NotFound";

// Public routes
const AuthLogin = Loadable(lazy(() => import("./pages/Login")));
const AuthRegister = Loadable(lazy(() => import("./pages/Register")));
const CreatePassword = Loadable(lazy(() => import("./pages/CreatePassword")));
const AuthForgotPassword = Loadable(
  lazy(() => import("./pages/ForgotPassword"))
);

// Public pages
const HomePage = Loadable(lazy(() => import("./pages/homepage/HomePage")));

// Private routes
const Dashboard = Loadable(lazy(() => import("./pages/Dashboard")));

// ==============================|| ROUTING RENDER ||============================== //

const AppRoutes = () => {
  return (
    <Routes>
      {/* Public Routes with Public Layout */}
      <Route path="/" element={<PublicLayout />}>
        <Route path="/" element={<HomePage />} />
      </Route>

      {/* Auth Routes */}
      <Route path="/" element={<GuestGuard />}>
        <Route path="/" element={<AuthLayout />}>
          <Route path="login" element={<AuthLogin />} />
          <Route path="register" element={<AuthRegister />} />
          <Route path="create-password" element={<CreatePassword />} />
          <Route path="forgot-password" element={<AuthForgotPassword />} />
        </Route>
      </Route>

      {/* Protected Routes with Dashboard Layout */}
      <Route path="/" element={<AuthGuard />}>
        <Route path="/" element={<DashboardLayout />}>
          <Route path="dashboard" element={<Dashboard />} />
        </Route>
      </Route>

      {/* Catch All */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
