// Homepage default values and configurations

export interface IHomepageSearchValues {
  jobTitle: string;
  location: string;
  category?: string;
  company?: string;
}

export const homepageSearchDefaultValues: IHomepageSearchValues = {
  jobTitle: "",
  location: "",
  category: "",
  company: "",
};

// Popular searches configuration
export const popularSearches = [
  "Designer",
  "Developer",
  "Web",
  "IOS",
  "PHP",
  "Senior",
  "Engineer",
];

// Job categories configuration
export const jobCategories = [
  { name: "Technology", count: 1250, icon: "💻" },
  { name: "Healthcare", count: 890, icon: "🏥" },
  { name: "Finance", count: 670, icon: "💰" },
  { name: "Education", count: 540, icon: "📚" },
  { name: "Marketing", count: 430, icon: "📈" },
  { name: "Design", count: 320, icon: "🎨" },
];

// Search form configuration
export const searchFormConfig = {
  jobTitle: {
    name: "jobTitle",
    placeholder: "Job title, keywords, or company",
  },
  location: {
    name: "location",
    placeholder: "City or postcode",
  },
};
