import { Typo<PERSON>, <PERSON>, <PERSON>, Card } from "antd";
import { useNavigate } from "react-router-dom";
import { Hero, PopularCategories } from "@/containers";

const { Title, Paragraph } = Typography;

// ==============================|| HOME PAGE ||============================== //

const HomePage = () => {
  const navigate = useNavigate();

  const handleSearch = (jobTitle: string, location: string) => {
    // Handle search functionality - can navigate to jobs page with search params
    console.log("Search:", { jobTitle, location });
    // navigate(`/jobs?title=${encodeURIComponent(jobTitle)}&location=${encodeURIComponent(location)}`);
  };

  const handleCategoryClick = (categoryId: string) => {
    // Handle category click - can navigate to jobs page filtered by category
    console.log("Category clicked:", categoryId);
    // navigate(`/jobs?category=${categoryId}`);
  };

  return (
    <div>
      {/* Hero Section */}
      <Hero onSearch={handleSearch} />

      <div style={{ padding: "48px 24px" }}>
        <Row gutter={[24, 48]}>
          {/* Job Categories Section */}
          <Col span={24}>
            <PopularCategories onCategoryClick={handleCategoryClick} />
          </Col>

          {/* Featured Jobs Section */}
          <Col span={24}>
            <Title
              level={2}
              style={{ textAlign: "center", marginBottom: "32px" }}
            >
              Featured Jobs
            </Title>
            <Row gutter={[24, 24]}>
              {/* This would be populated with actual job listings */}
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <Col xs={24} sm={12} lg={8} key={item}>
                  <Card hoverable>
                    <Title level={5}>Job Title {item}</Title>
                    <Paragraph>Company Name</Paragraph>
                    <Paragraph>Location • Full Time</Paragraph>
                  </Card>
                </Col>
              ))}
            </Row>
          </Col>

          {/* Additional sections would be added here */}
        </Row>
      </div>
    </div>
  );
};

export default HomePage;
