import { Card, Col, Row, Statistic, Typography } from "antd";
import {
  UserOutlined,
  FileSearchOutlined,
  CheckC<PERSON>cleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";

const { Title } = Typography;

// ==============================|| DASHBOARD PAGE ||============================== //

const Dashboard = () => {
  return (
    <>
      <Title level={2}>Dashboard</Title>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={1128}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Jobs"
              value={93}
              prefix={<FileSearchOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Applications"
              value={758}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending"
              value={31}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="Recent Activity">
            <p>No recent activity to display.</p>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default Dashboard;
