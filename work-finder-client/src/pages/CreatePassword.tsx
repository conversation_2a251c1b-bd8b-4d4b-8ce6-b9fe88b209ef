import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button, Form, Input, Typography, Result } from "antd";
import { LockOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

interface CreatePasswordFormData {
  password: string;
  confirmPassword: string;
}

// ==============================|| CREATE PASSWORD PAGE ||============================== //

const CreatePassword = () => {
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const navigate = useNavigate();

  const onFinish = (values: CreatePasswordFormData) => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSubmitted(true);

      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    }, 1000);
  };

  if (submitted) {
    return (
      <Result
        status="success"
        title="Password Created!"
        subTitle="Your password has been created successfully. You will be redirected to the login page."
      />
    );
  }

  return (
    <>
      <Title level={4} style={{ textAlign: "center", marginBottom: 24 }}>
        Create Password
      </Title>
      <Text type="secondary" style={{ display: "block", marginBottom: 24 }}>
        Please create a new password for your account.
      </Text>
      <Form name="createPassword" onFinish={onFinish} layout="vertical">
        <Form.Item
          name="password"
          rules={[
            { required: true, message: "Please input your Password!" },
            { min: 6, message: "Password must be at least 6 characters!" },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="New Password"
          />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            { required: true, message: "Please confirm your password!" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("The two passwords do not match!")
                );
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Confirm Password"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Create Password
          </Button>
        </Form.Item>
      </Form>
      <div style={{ textAlign: "center", marginTop: 16 }}>
        <Link to="/login">Back to Login</Link>
      </div>
    </>
  );
};

export default CreatePassword;
