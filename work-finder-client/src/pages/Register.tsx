import { useState } from "react";
import { Link } from "react-router-dom";
import { Button, Form, Input, Typography, Divider } from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// ==============================|| REGISTER PAGE ||============================== //

const Register = () => {
  const [loading, setLoading] = useState(false);

  const onFinish = (values: RegisterFormData) => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      console.log("Registration successful:", values);
      setLoading(false);
      // Redirect to login or verification page
    }, 1000);
  };

  return (
    <>
      <Title level={4} style={{ textAlign: "center", marginBottom: 24 }}>
        Register
      </Title>
      <Form name="register" onFinish={onFinish} layout="vertical">
        <Form.Item
          name="name"
          rules={[{ required: true, message: "Please input your Name!" }]}
        >
          <Input prefix={<UserOutlined />} placeholder="Full Name" />
        </Form.Item>
        <Form.Item
          name="email"
          rules={[
            { required: true, message: "Please input your Email!" },
            { type: "email", message: "Please enter a valid email!" },
          ]}
        >
          <Input prefix={<MailOutlined />} placeholder="Email" />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[
            { required: true, message: "Please input your Password!" },
            { min: 6, message: "Password must be at least 6 characters!" },
          ]}
        >
          <Input.Password prefix={<LockOutlined />} placeholder="Password" />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            { required: true, message: "Please confirm your password!" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("The two passwords do not match!")
                );
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Confirm Password"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Register
          </Button>
        </Form.Item>
      </Form>
      <Divider plain>Or</Divider>
      <div style={{ textAlign: "center" }}>
        <Text>Already have an account?</Text> <Link to="/login">Login</Link>
      </div>
    </>
  );
};

export default Register;
