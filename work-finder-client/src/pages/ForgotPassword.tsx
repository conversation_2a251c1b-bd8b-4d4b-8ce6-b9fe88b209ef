import { useState } from "react";
import { Link } from "react-router-dom";
import { Button, Form, Input, Typography, Result } from "antd";
import { MailOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

interface ForgotPasswordFormData {
  email: string;
}

// ==============================|| FORGOT PASSWORD PAGE ||============================== //

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [email, setEmail] = useState("");

  const onFinish = (values: ForgotPasswordFormData) => {
    setLoading(true);
    setEmail(values.email);

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSubmitted(true);
    }, 1000);
  };

  if (submitted) {
    return (
      <Result
        status="success"
        title="Reset link sent!"
        subTitle={`We have sent a password reset link to ${email}. Please check your inbox.`}
        extra={[
          <Button type="primary" key="login">
            <Link to="/login">Back to Login</Link>
          </Button>,
        ]}
      />
    );
  }

  return (
    <>
      <Title level={4} style={{ textAlign: "center", marginBottom: 24 }}>
        Forgot Password
      </Title>
      <Text type="secondary" style={{ display: "block", marginBottom: 24 }}>
        Enter your email address and we'll send you a link to reset your
        password.
      </Text>
      <Form name="forgotPassword" onFinish={onFinish} layout="vertical">
        <Form.Item
          name="email"
          rules={[
            { required: true, message: "Please input your Email!" },
            { type: "email", message: "Please enter a valid email!" },
          ]}
        >
          <Input prefix={<MailOutlined />} placeholder="Email" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Send Reset Link
          </Button>
        </Form.Item>
      </Form>
      <div style={{ textAlign: "center", marginTop: 16 }}>
        <Link to="/login">Back to Login</Link>
      </div>
    </>
  );
};

export default ForgotPassword;
