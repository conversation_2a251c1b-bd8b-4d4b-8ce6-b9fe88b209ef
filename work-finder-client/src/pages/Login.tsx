import { useState } from "react";
import { Link } from "react-router-dom";
import { Button, Checkbox, Form, Input, Typography, Divider } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../app/hooks";
import { setUser } from "../store/slice/authSlice";

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
  remember: boolean;
}

// ==============================|| LOGIN PAGE ||============================== //

const Login = () => {
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  const onFinish = (values: LoginFormData) => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      dispatch(setUser({ name: "Test User", email: values.email }));
      setLoading(false);
    }, 1000);
  };

  return (
    <>
      <Title level={4} style={{ textAlign: "center", marginBottom: 24 }}>
        Login
      </Title>
      <Form
        name="login"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        layout="vertical"
      >
        <Form.Item
          name="email"
          rules={[{ required: true, message: "Please input your Email!" }]}
        >
          <Input prefix={<UserOutlined />} placeholder="Email" />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: "Please input your Password!" }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            type="password"
            placeholder="Password"
          />
        </Form.Item>
        <Form.Item>
          <Form.Item name="remember" valuePropName="checked" noStyle>
            <Checkbox>Remember me</Checkbox>
          </Form.Item>

          <Link to="/forgot-password" style={{ float: "right" }}>
            Forgot password?
          </Link>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Log in
          </Button>
        </Form.Item>
      </Form>
      <Divider plain>Or</Divider>
      <div style={{ textAlign: "center" }}>
        <Text>Don't have an account?</Text> <Link to="/register">Register</Link>
      </div>
    </>
  );
};

export default Login;
