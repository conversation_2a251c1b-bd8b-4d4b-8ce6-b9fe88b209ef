import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { useNavigate } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import { CrownOutlined } from "@ant-design/icons";

import { closeDeniedPermission } from "@/store/slice/deniedPermissionSlice";
import { useAppDispatch, useAppSelector } from "@/app/hooks";
import { PUBLIC_URL } from "@/constants/Common";

interface Props {
  children?: React.ReactNode;
}

const DeniedPermissionScreen = ({ children }: Props) => {
  const { show, isTabWrap } = useAppSelector((state) => state.deniedPermission);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleClose = () => {
    navigate("/");
    dispatch(closeDeniedPermission());
  };

  return children && !show && !isTabWrap ? (
    children
  ) : (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "100%",
        background: `url("${PUBLIC_URL}background-dashboard.svg") no-repeat center`,
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: "50%",
          flexDirection: "column",
          gap: 20,
          padding: "40px 0",
        }}
      >
        <div
          style={{
            padding: 16,
            border: "2px solid #f99e2185",
            borderRadius: 30,
          }}
        >
          <CrownOutlined style={{ fontSize: 24, color: "#f99e21" }} />
        </div>
        <Typography.Title level={3} style={{ textAlign: "center" }}>
          Upgrade to Access the full feature of InstantView
        </Typography.Title>
        <Typography.Paragraph style={{ fontSize: 16, textAlign: "center" }}>
          Unlock limitless content possibilities. Upgrade now to exceed your
          credit limit and access valuable content that fuels creativity. Get
          more credit and unleash your full potential with our paid plans.
        </Typography.Paragraph>

        <Button type="primary" size="large" onClick={handleClose}>
          <FormattedMessage id="upgrade-btn" />
        </Button>
      </div>
    </div>
  );
};

export default DeniedPermissionScreen;
