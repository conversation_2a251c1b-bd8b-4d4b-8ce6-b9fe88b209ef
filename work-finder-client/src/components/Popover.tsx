import React from "react";
import { Popover as AntPopover } from "antd";
import type { PopoverProps } from "antd";

interface IPopoverProps {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  children: React.ReactNode;
  title?: React.ReactNode;
  placement?: PopoverProps["placement"];
}

const Popover = (props: IPopoverProps) => {
  const {
    anchorEl,
    handleClose,
    children,
    title,
    placement = "bottom",
  } = props;
  const open = Boolean(anchorEl);

  return (
    <AntPopover
      open={open}
      onOpenChange={(visible) => {
        if (!visible) handleClose();
      }}
      title={title}
      content={children}
      trigger="click"
      placement={placement}
    >
      {anchorEl ? <span /> : null}
    </AntPopover>
  );
};

export default Popover;
