import { Button, Space, Typography } from "antd";
import { DownloadOutlined } from "@ant-design/icons";

// project imports
import { exportDocument } from "@/utils/common";

interface IButtonExportProps {
  label: string;
  url: string;
  query: any;
}

const ButtonExport = (props: IButtonExportProps) => {
  const { label, url, query } = props;

  const handleDownload = () => {
    exportDocument(url, query);
  };

  return (
    <Button onClick={handleDownload}>
      <Space align="center" size={8}>
        <DownloadOutlined />
        <Typography.Text strong>{label}</Typography.Text>
      </Space>
    </Button>
  );
};

export default ButtonExport;
