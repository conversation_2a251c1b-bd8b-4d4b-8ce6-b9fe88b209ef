import { Card, Row, Col, Typography } from "antd";
import { FormattedMessage } from "react-intl";
import styled from "@emotion/styled";

import imageBackground from "../assets/images/maintenance/img-error-bg.svg";
import imagePurple from "../assets/images/maintenance/img-error-purple.svg";
import imageBlue from "../assets/images/maintenance/img-error-blue.svg";
import imageText from "../assets/images/maintenance/img-error-text.svg";
import { gridSpacing } from "../constants/Common";

const CardMediaWrapper = styled.div`
  max-width: 720px;
  margin: 0 auto;
  position: relative;
`;

const ErrorWrapper = styled.div`
  max-width: 350px;
  margin: 0 auto;
  text-align: center;
`;

const ErrorCard = styled(Card)`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CardMediaBlock = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: 3s bounce ease-in-out infinite;
`;

const CardMediaBlue = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: 15s wings ease-in-out infinite;
`;

const CardMediaPurple = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  animation: 12s wings ease-in-out infinite;
`;

const ExpiredAccount = () => {
  return (
    <ErrorCard bordered={false}>
      <Card.Grid style={{ width: "100%", boxShadow: "none" }}>
        <Row justify="center" gutter={[gridSpacing * 8, gridSpacing * 8]}>
          <Col xs={24}>
            <CardMediaWrapper>
              <img
                src={imageBackground}
                alt="Slider5 image"
                style={{ width: "100%" }}
              />
              <CardMediaBlock src={imageText} alt="Slider 1 image" />
              <CardMediaBlue src={imageBlue} alt="Slider 2 image" />
              <CardMediaPurple src={imagePurple} alt="Slider 3 image" />
            </CardMediaWrapper>
          </Col>
          <Col xs={24}>
            <ErrorWrapper>
              <Row gutter={[gridSpacing * 8, gridSpacing * 8]}>
                <Col xs={24}>
                  <Typography.Title level={1}>
                    <FormattedMessage id="something-is-wrong" />
                  </Typography.Title>
                </Col>
                <Col xs={24}>
                  <Typography.Paragraph>
                    <FormattedMessage id="error-expired-account-screen-centent" />
                  </Typography.Paragraph>
                </Col>
              </Row>
            </ErrorWrapper>
          </Col>
        </Row>
      </Card.Grid>
    </ErrorCard>
  );
};

export default ExpiredAccount;
