import { Button, Upload, Space } from "antd";
import { UploadOutlined } from "@ant-design/icons";

// project imports
import { useAppDispatch } from "@/app/hooks";
import { openSnackbar } from "@/store/slice/snackbarSlice";
import { validateFileFormat } from "@/utils/common";

interface IFileUploadSingleProps {
  loading?: boolean;
  selectedFile: File | null;
  handleChange: (file: File | null) => void;
  handleUpload: (file: File) => void;
  handleUploadConfirm: (file: File) => void;
}

const FileUploadSingle = ({
  selectedFile,
  loading,
  handleUpload,
  handleUploadConfirm,
  handleChange,
}: IFileUploadSingleProps) => {
  const dispatch = useAppDispatch();

  // On file select (from the pop up)
  const handleFileChange = (info: any) => {
    if (info.file.status !== "uploading") {
      handleChange(info.file.originFileObj);
      handleUploadConfirm(info.file.originFileObj);
    }
  };

  // On file upload (click the upload button)
  const handleFileUpload = () => {
    if (selectedFile && validateFileFormat(selectedFile)) {
      handleUpload(selectedFile);
    } else {
      dispatch(
        openSnackbar({
          open: true,
          message: "error-file",
          variant: "alert",
          alert: { color: "error" },
        })
      );
    }
    handleChange(null);
  };

  return (
    <Space>
      <Upload
        beforeUpload={() => false}
        onChange={handleFileChange}
        showUploadList={false}
        accept=".xlsx, .xls"
      >
        <Button>
          {selectedFile ? `${selectedFile.name}` : "Choose file..."}
        </Button>
      </Upload>
      <Button
        loading={loading}
        disabled={!selectedFile}
        type="primary"
        size="small"
        icon={<UploadOutlined />}
        onClick={handleFileUpload}
      >
        Upload
      </Button>
    </Space>
  );
};

export default FileUploadSingle;
