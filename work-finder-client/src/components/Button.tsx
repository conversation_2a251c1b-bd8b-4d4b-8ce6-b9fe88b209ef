import React, { ReactNode } from "react";
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "antd";
import type { ButtonProps } from "antd";

interface IButtonProps {
  children: ReactNode;
  buttonProps?: ButtonProps;
  type?: "primary" | "default" | "dashed" | "link" | "text";
  size?: "small" | "middle" | "large";
  block?: boolean;
}

const Button = (props: IButtonProps) => {
  const { children, type, size, block = true, ...buttonProps } = props;
  return (
    <AntButton type={type} size={size} block={block} {...buttonProps}>
      {children}
    </AntButton>
  );
};

export default Button;
