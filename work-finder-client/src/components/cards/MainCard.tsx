import React, { forwardRef, ReactNode } from "react";
import { Card, Divider, Skeleton, theme } from "antd";
import type { CardProps } from "antd";

// ==============================|| MAIN CARD ||============================== //

interface MainCardProps extends Omit<CardProps, "title" | "content"> {
  border?: boolean;
  boxShadow?: boolean;
  children?: ReactNode;
  content?: boolean;
  contentClass?: string;
  contentSX?: React.CSSProperties;
  darkTitle?: boolean;
  secondary?: ReactNode;
  shadow?: string;
  sx?: React.CSSProperties;
  title?: ReactNode;
  elevation?: number;
  codeHighlight?: boolean;
  codeString?: string;
  modal?: boolean;
  isLoading?: boolean;
}

const MainCard = forwardRef<HTMLDivElement, MainCardProps>(
  (
    {
      border = true,
      boxShadow,
      children,
      content = true,
      contentClass = "",
      contentSX = {},
      darkTitle,
      secondary,
      shadow,
      sx = {},
      title,
      elevation,
      codeHighlight,
      codeString,
      modal = false,
      isLoading = false,
      ...others
    },
    ref
  ) => {
    const { token } = theme.useToken();

    const cardStyle: React.CSSProperties = {
      position: "relative",
      border: border ? "1px solid" : "none",
      borderColor: token.colorBorderSecondary,
      borderRadius: token.borderRadius,
      boxShadow: boxShadow
        ? shadow || `0px 2px 8px ${token.colorBgElevated}`
        : "none",
      ...sx,
    };

    const cardContentStyle: React.CSSProperties = {
      padding: content ? token.padding : 0,
      ...contentSX,
    };

    const cardTitle = title && (
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <span
          style={{
            color: darkTitle ? token.colorTextSecondary : token.colorText,
            fontSize: token.fontSizeLG,
            fontWeight: 500,
          }}
        >
          {title}
        </span>
        {secondary && <div>{secondary}</div>}
      </div>
    );

    return (
      <Card
        ref={ref}
        title={cardTitle}
        style={cardStyle}
        bordered={border}
        {...others}
      >
        {isLoading ? (
          <Skeleton active paragraph={{ rows: 5 }} />
        ) : (
          <div className={contentClass} style={cardContentStyle}>
            {children}
          </div>
        )}
        {codeHighlight && codeString && (
          <>
            <Divider style={{ margin: "24px 0" }} />
            <pre
              style={{
                margin: 0,
                borderRadius: token.borderRadius,
                padding: token.padding,
                backgroundColor: token.colorBgContainer,
              }}
            >
              <code>{codeString}</code>
            </pre>
          </>
        )}
      </Card>
    );
  }
);

export default MainCard;
