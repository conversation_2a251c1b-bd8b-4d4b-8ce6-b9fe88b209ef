import React from "react";
import { Typography } from "antd";
import MainCard from "./MainCard";

const { Text } = Typography;

// ==============================|| FLOATING CARD ||============================== //

interface FloatingCardProps {
  title: string;
  subtitle: string;
  dotColor: string;
  icon?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
}

const FloatingCard: React.FC<FloatingCardProps> = ({
  title,
  subtitle,
  dotColor,
  icon,
  style = {},
  className = "",
}) => {
  return (
    <MainCard
      content={false}
      border={false}
      boxShadow={true}
      shadow="0 8px 25px rgba(0, 0, 0, 0.15)"
      sx={{
        padding: "16px 20px",
        borderRadius: "12px",
        background: "white",
        ...style,
      }}
      className={className}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        {icon && (
          <div
            style={{
              width: "40px",
              height: "40px",
              background: "#4F46E5",
              borderRadius: "8px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "20px",
            }}
          >
            {icon}
          </div>
        )}
        <div style={{ flex: 1 }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              marginBottom: "4px",
            }}
          >
            <div
              style={{
                width: "8px",
                height: "8px",
                background: dotColor,
                borderRadius: "50%",
              }}
            />
            <Text strong>{title}</Text>
          </div>
          <Text style={{ fontSize: "14px", color: "#666" }}>{subtitle}</Text>
        </div>
      </div>
    </MainCard>
  );
};

export default FloatingCard;
