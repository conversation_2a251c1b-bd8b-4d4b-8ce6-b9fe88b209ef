import { Typography, Space, theme } from "antd";
import { Link } from "react-router-dom";

// ==============================|| AUTH FOOTER ||============================== //

const AuthFooter = () => {
  const { token } = theme.useToken();

  return (
    <Space
      direction="vertical"
      align="center"
      style={{ width: "100%", marginTop: token.marginLG }}
    >
      <Typography.Text type="secondary">&copy; 2023 WorkFinder</Typography.Text>
      <Space size={token.size}>
        <Link to="#">
          <Typography.Text type="secondary">Terms of Service</Typography.Text>
        </Link>
        <Typography.Text type="secondary">|</Typography.Text>
        <Link to="#">
          <Typography.Text type="secondary">Privacy Policy</Typography.Text>
        </Link>
      </Space>
    </Space>
  );
};

export default AuthFooter;
