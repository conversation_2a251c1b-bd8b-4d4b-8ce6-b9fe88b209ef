import React, { ReactNode } from "react";
import { Card, theme } from "antd";
import type { CardProps } from "antd";

// ==============================|| SUB CARD ||============================== //

interface SubCardProps extends Omit<CardProps, "title" | "content"> {
  children: ReactNode;
  content?: boolean;
  contentClass?: string;
  contentSX?: React.CSSProperties;
  darkTitle?: boolean;
  secondary?: ReactNode;
  sx?: React.CSSProperties;
  contentProps?: React.HTMLAttributes<HTMLDivElement>;
  title?: ReactNode;
}

const SubCard = ({
  children,
  content = true,
  contentClass = "",
  contentSX = {},
  darkTitle,
  secondary,
  sx = {},
  contentProps,
  title,
  ...others
}: SubCardProps) => {
  const { token } = theme.useToken();

  const cardStyle: React.CSSProperties = {
    border: "1px solid",
    borderColor: token.colorBorderSecondary,
    borderRadius: token.borderRadius,
    ...sx,
  };

  const cardContentStyle: React.CSSProperties = {
    padding: content ? token.padding : 0,
    ...contentSX,
  };

  const cardTitle = title && (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <span
        style={{
          color: darkTitle ? token.colorTextSecondary : token.colorText,
          fontSize: token.fontSizeLG,
          fontWeight: 500,
        }}
      >
        {title}
      </span>
      {secondary && <div>{secondary}</div>}
    </div>
  );

  return (
    <Card title={cardTitle} style={cardStyle} bordered {...others}>
      <div className={contentClass} style={cardContentStyle} {...contentProps}>
        {children}
      </div>
    </Card>
  );
};

export default SubCard;
