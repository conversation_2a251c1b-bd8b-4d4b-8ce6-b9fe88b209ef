import React from "react";
import { Input } from "antd";

// ==============================|| TEXT CONDITION ||============================== //

interface TextConditionProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  style?: React.CSSProperties;
}

const TextCondition = ({
  value,
  onChange,
  placeholder = "Enter text",
  className,
  style,
}: TextConditionProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <Input
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      style={{ width: "100%", ...style }}
    />
  );
};

export default TextCondition;
