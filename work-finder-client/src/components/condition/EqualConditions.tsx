import React from "react";
import { Select } from "antd";

// ==============================|| EQUAL CONDITIONS ||============================== //

interface EqualConditionsProps {
  value?: string;
  onChange?: (value: string) => void;
  options?: string[];
  placeholder?: string;
  className?: string;
  style?: React.CSSProperties;
}

const EqualConditions = ({
  value,
  onChange,
  options = [],
  placeholder = "Select condition",
  className,
  style,
}: EqualConditionsProps) => {
  const handleChange = (selectedValue: string) => {
    if (onChange) {
      onChange(selectedValue);
    }
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      style={{ width: "100%", ...style }}
      options={options.map((option) => ({ label: option, value: option }))}
    />
  );
};

export default EqualConditions;
