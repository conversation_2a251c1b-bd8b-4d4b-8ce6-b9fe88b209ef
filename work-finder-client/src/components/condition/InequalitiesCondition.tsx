import { useEffect, useState } from "react";
import { Row, Col, Select, DatePicker, InputNumber, Form } from "antd";
import { useForm, Controller, FormProvider } from "react-hook-form";
import dayjs from "dayjs";

// Constants
const INEQUALITIES_CONDITIONS = [
  { value: "equal", label: "Equal" },
  { value: "notEqual", label: "Not Equal" },
  { value: "greater", label: "Greater Than" },
  { value: "greaterOrEqual", label: "Greater Than or Equal" },
  { value: "less", label: "Less Than" },
  { value: "lessOrEqual", label: "Less Than or Equal" },
  { value: "between", label: "Between" },
];

const PERCENT_PLACEHOLDER = "%";

// ==============================|| INEQUALITIES CONDITION ||============================== //

type InequalitiesConditionProps = {
  conditionName: string;
  conditionValueName: string;
  minValueName: string;
  maxValueName: string;
  isPercentage?: boolean;
  isDatePicker?: boolean;
  form?: any; // Form context from parent
};

const InequalitiesCondition = ({
  conditionName,
  conditionValueName,
  minValueName,
  maxValueName,
  isPercentage,
  isDatePicker,
  form,
}: InequalitiesConditionProps) => {
  const [isBetweenValue, setIsBetweenValue] = useState(false);

  // Check if form is provided from parent or create a local one
  const formMethods = form || useForm();

  const handleChangeCondition = (value: string) => {
    const today = new Date();
    if (value === "between") {
      setIsBetweenValue(true);
      formMethods.setValue(minValueName, isDatePicker ? today : 0);
      formMethods.setValue(
        maxValueName,
        isDatePicker ? new Date(today.setDate(today.getDate() + 1)) : 1
      );
      formMethods.setValue(conditionValueName, undefined);
    } else {
      setIsBetweenValue(false);
      formMethods.setValue(
        conditionValueName,
        isDatePicker
          ? today
          : formMethods.getValues(conditionValueName) !== undefined
          ? formMethods.getValues(conditionValueName)
          : ""
      );
      formMethods.setValue(minValueName, undefined);
      formMethods.setValue(maxValueName, undefined);
    }
  };

  useEffect(() => {
    if (formMethods.getValues(conditionName) === "between") {
      setIsBetweenValue(true);
    } else {
      setIsBetweenValue(false);
    }
  }, [conditionName, formMethods, isDatePicker]);

  // Wrap with FormProvider only if form is not provided from parent
  const content = (
    <Row gutter={8}>
      <Col span={6}>
        <Controller
          name={conditionName}
          control={formMethods.control}
          render={({ field }) => (
            <Select
              {...field}
              options={INEQUALITIES_CONDITIONS}
              style={{ width: "100%" }}
              onChange={(value) => {
                field.onChange(value);
                handleChangeCondition(value);
              }}
            />
          )}
        />
      </Col>

      {isDatePicker ? (
        <>
          {isBetweenValue && minValueName && maxValueName ? (
            <>
              <Col span={9}>
                <Controller
                  name={minValueName}
                  control={formMethods.control}
                  render={({ field }) => (
                    <DatePicker
                      style={{ width: "100%" }}
                      value={field.value ? dayjs(field.value) : null}
                      onChange={(date) =>
                        field.onChange(date ? date.toDate() : null)
                      }
                    />
                  )}
                />
              </Col>
              <Col span={9}>
                <Controller
                  name={maxValueName}
                  control={formMethods.control}
                  render={({ field }) => (
                    <DatePicker
                      style={{ width: "100%" }}
                      value={field.value ? dayjs(field.value) : null}
                      onChange={(date) =>
                        field.onChange(date ? date.toDate() : null)
                      }
                    />
                  )}
                />
              </Col>
            </>
          ) : (
            <Col span={9}>
              <Controller
                name={conditionValueName}
                control={formMethods.control}
                render={({ field }) => (
                  <DatePicker
                    style={{ width: "100%" }}
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date ? date.toDate() : null)
                    }
                  />
                )}
              />
            </Col>
          )}
        </>
      ) : (
        <>
          {isBetweenValue && minValueName && maxValueName ? (
            <>
              <Col span={9}>
                <Controller
                  name={minValueName}
                  control={formMethods.control}
                  render={({ field }) => (
                    <InputNumber
                      style={{ width: "100%" }}
                      {...field}
                      placeholder={
                        isPercentage ? PERCENT_PLACEHOLDER : undefined
                      }
                      formatter={
                        isPercentage ? (value) => `${value}%` : undefined
                      }
                      parser={
                        isPercentage
                          ? (value) => value!.replace("%", "")
                          : undefined
                      }
                    />
                  )}
                />
              </Col>
              <Col span={9}>
                <Controller
                  name={maxValueName}
                  control={formMethods.control}
                  render={({ field }) => (
                    <InputNumber
                      style={{ width: "100%" }}
                      {...field}
                      placeholder={
                        isPercentage ? PERCENT_PLACEHOLDER : undefined
                      }
                      formatter={
                        isPercentage ? (value) => `${value}%` : undefined
                      }
                      parser={
                        isPercentage
                          ? (value) => value!.replace("%", "")
                          : undefined
                      }
                    />
                  )}
                />
              </Col>
            </>
          ) : (
            <Col span={9}>
              <Controller
                name={conditionValueName}
                control={formMethods.control}
                render={({ field }) => (
                  <InputNumber
                    style={{ width: "100%" }}
                    {...field}
                    placeholder={isPercentage ? PERCENT_PLACEHOLDER : undefined}
                    formatter={
                      isPercentage ? (value) => `${value}%` : undefined
                    }
                    parser={
                      isPercentage
                        ? (value) => value!.replace("%", "")
                        : undefined
                    }
                  />
                )}
              />
            </Col>
          )}
        </>
      )}
    </Row>
  );

  return form ? (
    content
  ) : (
    <FormProvider {...formMethods}>{content}</FormProvider>
  );
};

export default InequalitiesCondition;
