import { useEffect, useState, ReactElement } from "react";
import { Collapse, theme } from "antd";
import { DownOutlined } from "@ant-design/icons";

const { Panel } = Collapse;

type AccordionItem = {
  id: string;
  title: ReactElement | string;
  content: ReactElement | string;
  disabled?: boolean;
  expanded?: boolean;
  defaultExpand?: boolean | undefined;
};

interface accordionProps {
  data: AccordionItem[];
  defaultExpandedId?: string | boolean | null;
  expandIcon?: ReactElement;
  square?: boolean;
  toggle?: boolean;
}

// ==============================|| ACCORDION ||============================== //

const Accordion = ({
  data,
  defaultExpandedId = null,
  expandIcon,
  square,
  toggle,
}: accordionProps) => {
  const { token } = theme.useToken();

  const [expanded, setExpanded] = useState<string[] | undefined>(undefined);

  useEffect(() => {
    if (defaultExpandedId) {
      if (typeof defaultExpandedId === "string") {
        setExpanded([defaultExpandedId]);
      } else if (defaultExpandedId === true) {
        setExpanded(data.map((item) => item.id));
      }
    } else {
      const expandedItems = data
        .filter((item) => !item.disabled && item.defaultExpand)
        .map((item) => item.id);

      setExpanded(expandedItems.length > 0 ? expandedItems : undefined);
    }
  }, [defaultExpandedId, data]);

  const handleChange = (keys: string | string[]) => {
    if (toggle) {
      setExpanded(Array.isArray(keys) ? keys : [keys]);
    }
  };

  return (
    <Collapse
      activeKey={expanded}
      onChange={handleChange}
      bordered={!square}
      expandIcon={({ isActive }) =>
        expandIcon ||
        (expandIcon === false ? null : (
          <DownOutlined rotate={isActive ? 180 : 0} />
        ))
      }
      style={{ width: "100%" }}
    >
      {data &&
        data.map((item: AccordionItem) => (
          <Panel
            key={item.id}
            header={item.title}
            disabled={item.disabled}
            style={{
              color: token.colorTextSecondary,
              fontWeight: 500,
            }}
          >
            {item.content}
          </Panel>
        ))}
    </Collapse>
  );
};

export default Accordion;
