import React from "react";

interface TreeItemProps {
  nodeId: string;
  label: React.ReactNode;
  children?: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
  selected?: boolean;
  onClick?: (event: React.MouseEvent, nodeId: string) => void;
  onIconClick?: (event: React.MouseEvent) => void;
  className?: string;
  style?: React.CSSProperties;
  itemId?: string;
}

/**
 * TreeItem component for use with Ant Design Tree
 * This component serves as a data structure for TreeView
 * It doesn't render anything on its own, but is used by TreeView to build the tree structure
 */
const TreeItem = (props: TreeItemProps) => {
  // This component doesn't actually render anything
  // It's just a wrapper for data that will be used by the TreeView component
  return <>{props.children}</>;
};

export default TreeItem;
