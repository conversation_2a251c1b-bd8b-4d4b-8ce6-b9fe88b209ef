import React from "react";
import { Tree, theme } from "antd";
import type { TreeProps } from "antd";
import { DownOutlined, RightOutlined } from "@ant-design/icons";

// Constants
const TREEITEM_DEFAULT_VALUE = { value: "root" };

interface TreeViewProps {
  children?: React.ReactNode;
  defaultExpanded?: string[];
  treeProps?: Partial<TreeProps>;
}

const TreeView: React.FC<TreeViewProps> = (props) => {
  const {
    treeProps,
    children,
    defaultExpanded = [TREEITEM_DEFAULT_VALUE.value],
  } = props;
  const { token } = theme.useToken();

  return (
    <div className="custom-tree-container">
      <style>
        {`
        .custom-tree-container .ant-tree-node-content-wrapper {
          height: 30px;
          margin: 2.5px 0;
        }
        `}
      </style>
      <Tree
        showLine
        switcherIcon={({ expanded }) =>
          expanded ? <DownOutlined /> : <RightOutlined />
        }
        defaultExpandedKeys={defaultExpanded}
        {...treeProps}
      >
        {children}
      </Tree>
    </div>
  );
};

export default TreeView;
