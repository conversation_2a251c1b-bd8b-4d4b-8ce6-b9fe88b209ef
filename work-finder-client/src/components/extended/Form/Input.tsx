import React, { ChangeEvent, ReactNode, memo } from "react";
import { Input as AntInput, Form } from "antd";
import { Controller, useFormContext } from "react-hook-form";
import Label from "./Label";

// utils
const removeExtraSpace = (value: string) => {
  if (typeof value === "string") {
    return value.replace(/\s+/g, " ").trim();
  }
  return value;
};

interface InputProps {
  name: string;
  label?: string | ReactNode;
  disabled?: boolean;
  required?: boolean;
  onChangeInput?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  type?: React.HTMLInputTypeAttribute;
  placeholder?: string;
  style?: React.CSSProperties;
  styleLabel?: React.CSSProperties;
  // Additional Ant Design props
  size?: "large" | "middle" | "small";
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  allowClear?: boolean;
  // HTML input attributes we want to pass through
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  readOnly?: boolean;
}

const Input = (props: InputProps): React.ReactElement => {
  const {
    name,
    label,
    disabled,
    required,
    type,
    onChangeInput,
    placeholder,
    style,
    styleLabel,
    size = "middle",
    addonBefore,
    addonAfter,
    prefix,
    suffix,
    allowClear,
    ...rest
  } = props;

  const methods = useFormContext();

  return (
    <Controller
      name={name}
      control={methods.control}
      render={({
        field: { value, ref, onChange, ...field },
        fieldState: { error },
      }) => {
        return (
          <Form.Item
            validateStatus={error ? "error" : undefined}
            help={error?.message}
            style={{ marginBottom: 16 }}
          >
            {label && (
              <Label
                name={name}
                label={label}
                required={required}
                style={styleLabel}
              />
            )}
            <AntInput
              type={type}
              id={name}
              {...field}
              value={value}
              size={size}
              disabled={disabled}
              onBlur={() => {
                if (typeof value === "string") {
                  return onChange(removeExtraSpace(value));
                }
              }}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                onChange(e);
                onChangeInput?.(e);
              }}
              placeholder={placeholder}
              ref={ref}
              style={style}
              addonBefore={addonBefore}
              addonAfter={addonAfter}
              prefix={prefix}
              suffix={suffix}
              allowClear={allowClear}
              {...rest}
            />
          </Form.Item>
        );
      }}
    />
  );
};

export default memo(Input);
