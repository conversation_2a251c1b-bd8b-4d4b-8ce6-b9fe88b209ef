import React, { ReactNode } from "react";
import { Typography, theme } from "antd";

type LabelProps = {
  name: string;
  label: string | ReactNode;
  required?: boolean;
  style?: React.CSSProperties;
};

const Label = ({
  name = "",
  label = "",
  required = false,
  style,
}: LabelProps): React.ReactElement => {
  const { token } = theme.useToken();

  return (
    <Typography.Text
      id={name}
      style={{
        display: "block",
        marginBottom: token.marginXXS,
        textTransform: "capitalize",
        ...style,
      }}
    >
      {label}
      {required && (
        <span style={{ color: token.colorError, marginLeft: 4 }}>*</span>
      )}
    </Typography.Text>
  );
};

export default Label;
