import React from "react";
import { Form } from "antd";
import type { FormInstance } from "antd";

// react-hook-form
import {
  FieldValues,
  FormProvider as R<PERSON><PERSON><PERSON><PERSON>,
  SubmitHandler,
  useForm,
  UseFormProps,
  UseFormReturn,
} from "react-hook-form";

interface IFormProviderProps<T extends FieldValues>
  extends Omit<React.FormHTMLAttributes<HTMLFormElement>, "onSubmit"> {
  form?: UseFormProps<T>;
  formReturn?: UseFormReturn<T>;
  onSubmit?: SubmitHandler<T>;
  children: React.ReactElement | React.ReactElement[] | any;
  formReset?: T;
  antForm?: FormInstance;
}

function FormProvider<T extends FieldValues>(
  props: IFormProviderProps<T>
): React.ReactElement {
  const { children, onSubmit, form, formReset, formReturn, antForm, ...other } =
    props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const methods = formReturn
    ? { ...formReturn }
    : useForm<T>({ ...form, mode: "all" });

  React.useEffect(() => {
    if (formReset) {
      methods.reset(formReset);
    }
  }, [formReset, methods]);

  // If Ant Design Form instance is provided, wrap with Form.Provider
  if (antForm) {
    return (
      <Form form={antForm} onFinish={onSubmit as any} {...other}>
        <RHFProvider {...methods}>
          {React.Children?.map(children, (child) => {
            return child?.props?.name
              ? React.createElement<T>(child.type, {
                  ...{
                    key: child.props.name,
                    ...methods.register,
                    ...child.props,
                  },
                })
              : child;
          })}
        </RHFProvider>
      </Form>
    );
  }

  // Otherwise use the standard react-hook-form FormProvider
  return (
    <RHFProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit!)} {...other}>
        {React.Children?.map(children, (child) => {
          return child?.props?.name
            ? React.createElement<T>(child.type, {
                ...{
                  key: child.props.name,
                  ...methods.register,
                  ...child.props,
                },
              })
            : child;
        })}
      </form>
    </RHFProvider>
  );
}

export default FormProvider;
