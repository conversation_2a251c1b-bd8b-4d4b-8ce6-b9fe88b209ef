import { ReactNode } from "react";
import { Avatar as AntAvatar, theme } from "antd";
import type { AvatarProps as AntAvatarProps } from "antd";

// ==============================|| AVATAR ||============================== //

// Omit the size property from AntAvatarProps to avoid type conflicts
interface AvatarProps extends Omit<AntAvatarProps, "size"> {
  alt?: string;
  src?: string;
  className?: string;
  color?: string;
  component?: React.ElementType;
  target?: "_blank" | "_self" | "_parent" | "_top";
  href?: string;
  children?: ReactNode;
  outline?: boolean;
  size?: "badge" | "xs" | "sm" | "md" | "lg" | "xl" | number;
}

const Avatar = ({
  className,
  color,
  outline,
  size,
  style,
  ...others
}: AvatarProps) => {
  const { token } = theme.useToken();

  // Generate color style based on color prop and outline
  const getColorStyle = () => {
    if (!color) return {};

    if (outline) {
      return {
        color: color,
        backgroundColor: token.colorBgContainer,
        border: "2px solid",
        borderColor: color,
      };
    }

    return {
      color: token.colorBgContainer,
      backgroundColor: color,
    };
  };

  // Convert size prop to pixel value
  const getSizeValue = () => {
    switch (size) {
      case "badge":
        return 28;
      case "xs":
        return 34;
      case "sm":
        return 40;
      case "lg":
        return 72;
      case "xl":
        return 82;
      case "md":
        return 60;
      default:
        return typeof size === "number" ? size : "default";
    }
  };

  const colorStyle = getColorStyle();
  const sizeValue = getSizeValue();

  return (
    <AntAvatar
      size={sizeValue}
      style={{ ...colorStyle, ...style }}
      className={className}
      {...others}
    />
  );
};

export default Avatar;
