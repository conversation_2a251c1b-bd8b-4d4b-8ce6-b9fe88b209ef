import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Breadcrumb, Card, Divider, Space, Typography, theme } from "antd";
import { HomeOutlined } from "@ant-design/icons";

// types
interface NavItemType {
  id?: string;
  title?: string;
  type?: string;
  url?: string;
  icon?: any;
  breadcrumbs?: boolean;
  children?: NavItemType[];
}

interface NavItemTypeObject {
  id?: string;
  title?: string;
  type?: string;
  children?: NavItemType[];
  icon?: any;
  items?: Array<NavItemType | NavItemTypeObject>;
}

interface BreadCrumbsProps {
  card?: boolean;
  divider?: boolean;
  icon?: boolean;
  icons?: boolean;
  maxItems?: number;
  navigation?: NavItemTypeObject;
  rightAlign?: boolean;
  separator?: React.ReactNode;
  title?: boolean;
  titleBottom?: boolean;
  sx?: React.CSSProperties;
}

// Constants
const BASE_PATH = ""; // Update this with your actual base path

// ==============================|| BREADCRUMBS ||============================== //

const Breadcrumbs = ({
  card = true,
  divider = true,
  icon = false,
  icons = false,
  maxItems,
  navigation,
  rightAlign = false,
  separator = "/",
  title = false,
  titleBottom = false,
  ...others
}: BreadCrumbsProps) => {
  const { token } = theme.useToken();

  const [main, setMain] = useState<NavItemType | undefined>();
  const [item, setItem] = useState<NavItemType>();

  useEffect(() => {
    navigation?.items?.map((menu: NavItemType | NavItemTypeObject) => {
      if (menu.type && menu.type === "group") {
        getCollapse(
          menu as {
            children: NavItemType[];
            type?: string;
            items?: Array<NavItemType | NavItemTypeObject>;
          }
        );
      }
      return false;
    });
  }, [navigation]);

  // set active item state
  const getCollapse = (menu: NavItemTypeObject) => {
    if (menu.children) {
      menu.children.filter((collapse: NavItemType) => {
        if (collapse.type && collapse.type === "collapse") {
          getCollapse(
            collapse as {
              children: NavItemType[];
              type?: string;
              items?: Array<NavItemType | NavItemTypeObject>;
            }
          );
        } else if (collapse.type && collapse.type === "item") {
          if (document.location.pathname === BASE_PATH + collapse.url) {
            setMain(menu);
            setItem(collapse);
          }
        }
        return false;
      });
    }
  };

  let mainContent;
  let itemContent;
  let breadcrumbContent = <Typography.Text />;
  let itemTitle: string = "";

  // collapse item
  if (main && main.type === "collapse") {
    const CollapseIcon = main.icon;
    mainContent = (
      <Link
        to="#"
        style={{
          display: "flex",
          alignItems: "center",
          color: token.colorPrimary,
          textDecoration: "none",
        }}
      >
        {icons && CollapseIcon && (
          <CollapseIcon
            style={{
              marginRight: token.marginXS,
              fontSize: "1rem",
              color: token.colorPrimary,
            }}
          />
        )}
        {main.title}
      </Link>
    );
  }

  // items
  if (item && item.type === "item") {
    itemTitle = item.title || "";
    const ItemIcon = item.icon;

    itemContent = (
      <span
        style={{
          display: "flex",
          alignItems: "center",
          color: token.colorTextSecondary,
        }}
      >
        {icons && ItemIcon && (
          <ItemIcon
            style={{
              marginRight: token.marginXS,
              fontSize: "1rem",
              color: token.colorPrimary,
            }}
          />
        )}
        {itemTitle}
      </span>
    );

    // main
    if (item.breadcrumbs !== false) {
      breadcrumbContent = (
        <Card
          style={{
            marginBottom: card === false ? 0 : token.margin,
            border:
              card === false
                ? "none"
                : `1px solid ${token.colorBorderSecondary}`,
            background: card === false ? "transparent" : token.colorBgContainer,
          }}
          {...others}
        >
          <div style={{ padding: card === false ? 0 : token.padding }}>
            <Space
              direction={rightAlign ? "horizontal" : "vertical"}
              style={{
                width: "100%",
                justifyContent: rightAlign ? "space-between" : "flex-start",
                alignItems: rightAlign ? "center" : "flex-start",
              }}
            >
              {title && !titleBottom && (
                <Typography.Title
                  level={3}
                  style={{ fontWeight: 500, margin: 0 }}
                >
                  {item.title}
                </Typography.Title>
              )}
              <Breadcrumb separator={separator}>
                <Breadcrumb.Item>
                  <Link
                    to="/"
                    style={{ display: "flex", alignItems: "center" }}
                  >
                    {icons && (
                      <HomeOutlined
                        style={{
                          marginRight: token.marginXS,
                          fontSize: "1rem",
                          color: token.colorPrimary,
                        }}
                      />
                    )}
                    {icon && (
                      <HomeOutlined
                        style={{ fontSize: "1rem", color: token.colorPrimary }}
                      />
                    )}
                    {!icon && "Dashboard"}
                  </Link>
                </Breadcrumb.Item>
                {mainContent && (
                  <Breadcrumb.Item>{mainContent}</Breadcrumb.Item>
                )}
                <Breadcrumb.Item>{itemContent}</Breadcrumb.Item>
              </Breadcrumb>
              {title && titleBottom && (
                <Typography.Title
                  level={3}
                  style={{ fontWeight: 500, margin: 0 }}
                >
                  {item.title}
                </Typography.Title>
              )}
            </Space>
          </div>
          {card === false && divider !== false && (
            <Divider style={{ borderColor: token.colorPrimary, margin: 0 }} />
          )}
        </Card>
      );
    }
  }

  return breadcrumbContent;
};

export default Breadcrumbs;
