import React from "react";
import { Empty } from "antd";

interface TableEmptyProps {
  bordered?: boolean;
  height?: string | number;
}

const TableEmpty: React.FC<TableEmptyProps> = ({
  height = "400px",
  bordered = true,
}) => {
  return (
    <div
      style={{
        height,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        border: bordered ? "1px solid #f0f0f0" : "none",
        borderRadius: "2px",
      }}
    >
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="No data" />
    </div>
  );
};

export default TableEmpty;
