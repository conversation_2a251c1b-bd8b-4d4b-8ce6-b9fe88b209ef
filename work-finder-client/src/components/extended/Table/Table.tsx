import { ReactNode } from "react";
import { Table as AntTable } from "antd";
import type { TableProps } from "antd";

// project imports
import TableEmpty from "./Empty";
import TableLoading from "./Loading";

interface CustomTableProps<T = any> {
  isLoading?: boolean;
  children?: ReactNode;
  heads?: ReactNode;
  data?: T[];
  footer?: ReactNode;
  height?: string | number;
  maxHeight?: number | string;
  heightTableEmpty?: string | number;
  style?: React.CSSProperties;
  borderedEmpty?: boolean;
  // Ant Design Table props that we want to pass through
  columns?: TableProps<T>["columns"];
  pagination?: TableProps<T>["pagination"];
  rowKey?: TableProps<T>["rowKey"];
  rowSelection?: TableProps<T>["rowSelection"];
  onChange?: TableProps<T>["onChange"];
  scroll?: TableProps<T>["scroll"];
  bordered?: TableProps<T>["bordered"];
  size?: TableProps<T>["size"];
}

function Table<T extends object = any>(props: CustomTableProps<T>) {
  const {
    isLoading,
    heads,
    data = [],
    maxHeight,
    children,
    footer,
    height,
    heightTableEmpty,
    style,
    borderedEmpty,
    columns,
    pagination,
    rowKey,
    rowSelection,
    onChange,
    scroll,
    bordered,
    size = "small",
    ...other
  } = props;

  // If using the children approach (for custom rendering)
  if (children) {
    return (
      <div style={{ maxHeight, height, ...style }}>
        {isLoading ? (
          <TableLoading />
        ) : data.length === 0 ? (
          <TableEmpty bordered={borderedEmpty} height={heightTableEmpty} />
        ) : (
          <>
            {heads}
            {children}
            {footer && <div>{footer}</div>}
          </>
        )}
      </div>
    );
  }

  // If using the columns approach (standard Ant Design Table)
  return (
    <AntTable
      columns={columns}
      dataSource={data}
      loading={isLoading}
      pagination={pagination}
      rowKey={rowKey}
      rowSelection={rowSelection}
      onChange={onChange}
      scroll={{ x: "max-content", y: maxHeight, ...scroll }}
      bordered={bordered}
      size={size}
      footer={footer ? () => footer : undefined}
      locale={{
        emptyText: (
          <TableEmpty bordered={borderedEmpty} height={heightTableEmpty} />
        ),
      }}
      style={{ height, ...style }}
      {...other}
    />
  );
}

Table.defaultProps = {
  maxHeight: 500,
  size: "small",
};

export default Table;
