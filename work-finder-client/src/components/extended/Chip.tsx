import { Tag, theme } from "antd";
import type { TagProps } from "antd";
import React from "react";

// ==============================|| CHIP ||============================== //

interface ChipProps extends Omit<TagProps, "color"> {
  chipcolor?:
    | "primary"
    | "secondary"
    | "success"
    | "error"
    | "warning"
    | "orange"
    | string;
  disabled?: boolean;
  label?: React.ReactNode;
  avatar?: React.ReactElement;
  onDelete?: () => void;
  onClick?: () => void;
  variant?: "filled" | "outlined";
}

const Chip = ({
  chipcolor = "primary",
  disabled = false,
  style = {},
  variant = "filled",
  label,
  avatar,
  onDelete,
  onClick,
  ...others
}: ChipProps) => {
  const { token } = theme.useToken();

  // Generate color style based on chipcolor and variant
  const getColorStyle = () => {
    if (disabled) {
      return variant === "outlined"
        ? {
            color: token.colorTextDisabled,
            borderColor: token.colorTextDisabled,
            backgroundColor: "transparent",
          }
        : {
            color: token.colorTextDisabled,
            backgroundColor: token.colorBgContainerDisabled,
          };
    }

    switch (chipcolor) {
      case "primary":
        return variant === "outlined"
          ? {
              color: token.colorPrimary,
              borderColor: token.colorPrimary,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorPrimary,
            };
      case "secondary":
        return variant === "outlined"
          ? {
              color: token.colorInfo,
              borderColor: token.colorInfo,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorInfo,
            };
      case "success":
        return variant === "outlined"
          ? {
              color: token.colorSuccess,
              borderColor: token.colorSuccess,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorSuccess,
            };
      case "error":
        return variant === "outlined"
          ? {
              color: token.colorError,
              borderColor: token.colorError,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorError,
            };
      case "warning":
        return variant === "outlined"
          ? {
              color: token.colorWarning,
              borderColor: token.colorWarning,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorWarning,
            };
      case "orange":
        // Using warning as orange substitute
        return variant === "outlined"
          ? {
              color: token.colorWarning,
              borderColor: token.colorWarning,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: token.colorWarning,
            };
      default:
        // For custom colors
        return variant === "outlined"
          ? {
              color: chipcolor,
              borderColor: chipcolor,
              backgroundColor: "transparent",
            }
          : {
              color: token.colorTextLightSolid,
              backgroundColor: chipcolor,
            };
    }
  };

  const chipStyle = {
    ...getColorStyle(),
    ...style,
  };

  return (
    <Tag
      style={chipStyle}
      onClick={disabled ? undefined : onClick}
      closable={!disabled && !!onDelete}
      onClose={onDelete}
      icon={avatar}
      {...others}
    >
      {label || others.children}
    </Tag>
  );
};

export default Chip;
