import React from "react";
import { Tabs as Ant<PERSON><PERSON><PERSON>, Button, Space, theme } from "antd";
import type { TabsProps } from "antd";
import { CloseOutlined, StarFilled } from "@ant-design/icons";

// Types
interface TabItem {
  name: string;
  value: number;
  permission_key?: string;
  erasable?: boolean;
  isDefault?: boolean;
}

interface CustomTabsProps {
  tabList: TabItem[];
  onChange: (value: number) => void;
  tabValue: number;
  otherAction?: React.ReactNode;
  handlDeletetab?: (value: number) => void;
  isNotMultiLanguage?: boolean;
}

const Tabs: React.FC<CustomTabsProps> = (props) => {
  const {
    tabList,
    onChange,
    tabValue,
    otherAction,
    isNotMultiLanguage,
    handlDeletetab,
  } = props;
  const { token } = theme.useToken();

  // Convert to Ant Design TabsProps format
  const items: TabsProps["items"] = tabList.map((tab, index) => ({
    key: String(tab.value),
    label: (
      <Space>
        {isNotMultiLanguage ? tab.name : tab.name}
        {tab.isDefault && (
          <StarFilled style={{ color: "#FFDD00", fontSize: 15 }} />
        )}
        {tab.erasable && (
          <Button
            type="text"
            size="small"
            icon={
              <CloseOutlined
                style={{ color: token.colorError, fontSize: 15 }}
              />
            }
            onClick={(e) => {
              e.stopPropagation();
              handlDeletetab?.(tab.value);
            }}
            style={{ padding: 0, minWidth: "auto", marginLeft: -8 }}
          />
        )}
      </Space>
    ),
    disabled: tab.permission_key ? false : false, // Replace with your permission check logic
  }));

  const handleChange = (activeKey: string) => {
    onChange(Number(activeKey));
  };

  return (
    <AntTabs
      activeKey={String(tabValue)}
      onChange={handleChange}
      items={items}
      tabBarExtraContent={otherAction}
      size="large"
      type="card"
      style={{ minHeight: 50 }}
    />
  );
};

export default Tabs;
