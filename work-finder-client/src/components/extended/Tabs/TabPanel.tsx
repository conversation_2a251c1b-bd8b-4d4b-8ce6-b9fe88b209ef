import React from "react";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  style?: React.CSSProperties;
}

const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, style, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      style={style}
      {...other}
    >
      {value === index && children}
    </div>
  );
};

export default TabPanel;
