import React from "react";
import { Tooltip, Row, Col, Typography } from "antd";
import { FormattedMessage } from "react-intl";

interface IOption {
  color: string;
  label: string;
}

type ColorNoteTooltipProps = {
  notes: IOption[];
  children: React.ReactNode;
  width?: string | number;
};

const ColorNoteTooltip = ({
  notes,
  children,
  width,
}: ColorNoteTooltipProps) => {
  return (
    <Tooltip
      color="#a6a6a6"
      title={
        <div style={{ width: width ? width : "120px" }}>
          {notes.map((type: IOption, index: number) => (
            <Row
              key={index}
              align="middle"
              style={{ padding: "5px" }}
              gutter={8}
            >
              <Col>
                <div
                  style={{
                    backgroundColor: type.color,
                    width: 15,
                    height: 10,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: "4px",
                  }}
                />
              </Col>
              <Col flex="auto">
                <Typography.Text style={{ color: "white", fontSize: 10 }}>
                  <FormattedMessage id={type.label} />
                </Typography.Text>
              </Col>
            </Row>
          ))}
        </div>
      }
      placement="top"
    >
      <div>{children}</div>
    </Tooltip>
  );
};

export default ColorNoteTooltip;
