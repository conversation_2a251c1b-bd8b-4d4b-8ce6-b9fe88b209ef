import { Progress } from "antd";
import styled from "@emotion/styled";

// styles
const LoaderWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1301;
  width: 100%;
`;

// ==============================|| LOADER ||============================== //

const Loader = () => (
  <LoaderWrapper>
    <Progress
      percent={100}
      showInfo={false}
      status="active"
      strokeColor="#1890ff"
    />
  </LoaderWrapper>
);

export default Loader;
