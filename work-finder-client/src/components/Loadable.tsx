import { Suspense } from "react";
import type { ComponentType } from "react";
import { Spin } from "antd";

// project imports
const Loader = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
    }}
  >
    <Spin size="large" />
  </div>
);

// ==============================|| LOADABLE - LAZY LOADING ||============================== //

const Loadable = (Component: ComponentType) => (props: any) =>
  (
    <Suspense fallback={<Loader />}>
      <Component {...props} />
    </Suspense>
  );

export default Loadable;
