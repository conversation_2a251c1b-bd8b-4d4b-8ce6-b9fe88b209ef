// Mock authorization util - clone từ vsii-internal-fe logic
import { store } from "@/app/store";

export function userAuthorization(permissionsRequired?: string[]) {
  let isAllowFunctions: boolean = true; // Default allow all for demo

  if (!permissionsRequired?.length) {
    return { isAllowFunctions };
  }

  // Mock user permissions - trong thực tế lấy từ store.getState().auth
  const userPermissions: string[] = [];

  if (userPermissions.length) {
    isAllowFunctions = permissionsRequired.some((permission) =>
      userPermissions.includes(permission)
    );
  }

  return { isAllowFunctions };
}
