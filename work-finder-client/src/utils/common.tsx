import axios from "axios";
import { saveAs } from "file-saver";

// export document
export const exportDocument = async (url: string, query: any) => {
  try {
    const response = await axios({
      url,
      method: "GET",
      responseType: "blob",
      params: query,
    });

    // Get filename from content-disposition header or use default
    const contentDisposition = response.headers["content-disposition"];
    let filename = "download.xlsx";

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch.length === 2) {
        filename = filenameMatch[1];
      }
    }

    saveAs(new Blob([response.data]), filename);
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// validate file format
export const validateFileFormat = (file: File) => {
  const validExtensions = [".xlsx", ".xls"];
  const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
  return validExtensions.includes(fileExtension);
};
