import { useEffect } from "react";
import { useNavigate, Outlet } from "react-router-dom";
import { useAppSelector } from "../../app/hooks";
import { authSelector } from "../../store/slice/authSlice";

// ==============================|| GUEST GUARD ||============================== //

const GuestGuard = () => {
  const { isLoggedIn } = useAppSelector(authSelector);
  const navigate = useNavigate();

  useEffect(() => {
    if (isLoggedIn) {
      navigate("/dashboard", { replace: true });
    }
  }, [isLoggedIn, navigate]);

  return !isLoggedIn ? <Outlet /> : null;
};

export default GuestGuard;
