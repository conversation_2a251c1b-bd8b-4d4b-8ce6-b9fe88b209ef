import { useEffect } from "react";
import { useNavigate, Outlet } from "react-router-dom";
import { useAppSelector } from "../../app/hooks";
import { authSelector } from "../../store/slice/authSlice";

// ==============================|| AUTH GUARD ||============================== //

const AuthGuard = () => {
  const { isLoggedIn } = useAppSelector(authSelector);
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoggedIn) {
      navigate("/login", { replace: true });
    }
  }, [isLoggedIn, navigate]);

  return isLoggedIn ? <Outlet /> : null;
};

export default AuthGuard;
