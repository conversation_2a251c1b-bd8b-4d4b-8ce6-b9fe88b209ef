import { createContext, useEffect, useState } from "react";
import type { ReactNode } from "react";

// Types
export interface CustomizationProps {
  fontFamily: string;
  borderRadius: number;
  outlinedFilled: boolean;
  navType: string;
  presetColor: string;
  locale: string;
  onChangeMenuType: (navType: string) => void;
  onChangePresetColor: (presetColor: string) => void;
  onChangeBorderRadius: (borderRadius: number) => void;
  onChangeLocale: (locale: string) => void;
}

// Default context values
const initialState: CustomizationProps = {
  fontFamily: `'Roboto', sans-serif`,
  borderRadius: 8,
  outlinedFilled: true,
  navType: "light",
  presetColor: "#1890ff",
  locale: "en",
  onChangeMenuType: () => {},
  onChangePresetColor: () => {},
  onChangeBorderRadius: () => {},
  onChangeLocale: () => {},
};

// Create context
const ConfigContext = createContext(initialState);

interface ConfigProviderProps {
  children: ReactNode;
}

// Provider component
export const ConfigProvider = ({ children }: ConfigProviderProps) => {
  const [config, setConfig] = useState({
    fontFamily: initialState.fontFamily,
    borderRadius: initialState.borderRadius,
    outlinedFilled: initialState.outlinedFilled,
    navType: initialState.navType,
    presetColor: initialState.presetColor,
    locale: initialState.locale,
  });

  // Handler functions
  const onChangeMenuType = (navType: string) => {
    setConfig({
      ...config,
      navType,
    });
  };

  const onChangePresetColor = (presetColor: string) => {
    setConfig({
      ...config,
      presetColor,
    });
  };

  const onChangeBorderRadius = (borderRadius: number) => {
    setConfig({
      ...config,
      borderRadius,
    });
  };

  const onChangeLocale = (locale: string) => {
    setConfig({
      ...config,
      locale,
    });
  };

  // Load saved settings from localStorage
  useEffect(() => {
    const savedConfig = localStorage.getItem("workFinderConfig");
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig(parsedConfig);
      } catch (error) {
        console.error("Failed to parse saved config", error);
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem("workFinderConfig", JSON.stringify(config));
  }, [config]);

  return (
    <ConfigContext.Provider
      value={{
        ...config,
        onChangeMenuType,
        onChangePresetColor,
        onChangeBorderRadius,
        onChangeLocale,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
};

export default ConfigContext;
