import { Outlet, useLocation } from "react-router-dom";
import { Layout, theme } from "antd";
import PublicHeader from "@/layout/Header/PublicHeader";
import Footer from "@/layout/Footer";

const { Content } = Layout;

// ==============================|| PUBLIC LAYOUT ||============================== //

const PublicLayout = () => {
  const location = useLocation();
  const isHomepage = location.pathname === "/";
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <PublicHeader />
      <Content
        style={{
          background: isHomepage ? "transparent" : colorBgContainer,
          minHeight: "100vh",
          paddingTop: isHomepage ? "0" : "64px", // Add padding for other pages
        }}
      >
        <Outlet />
      </Content>
      <Footer />
    </Layout>
  );
};

export default PublicLayout;
