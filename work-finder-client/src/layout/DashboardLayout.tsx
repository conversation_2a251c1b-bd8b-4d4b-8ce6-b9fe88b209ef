import { Outlet } from "react-router-dom";
import { Layout, theme } from "antd";
import Sidebar from "@/layout/MenuList/Sidebar";
import Footer from "@/layout/Footer";

const { Header, Content } = Layout;

// ==============================|| DASHBOARD LAYOUT ||============================== //

const DashboardLayout = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <Sidebar />
      <Layout>
        <Header style={{ padding: 0, background: colorBgContainer }}>
          {/* Header content if needed */}
        </Header>
        <Content
          style={{
            margin: "24px 16px",
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          <Outlet />
        </Content>
        <Footer />
      </Layout>
    </Layout>
  );
};

export default DashboardLayout;
