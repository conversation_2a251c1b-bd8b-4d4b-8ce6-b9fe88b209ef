import { Layout, Row, Col, Typography, Space, Divider } from "antd";
import { Link } from "react-router-dom";
import {
  FacebookOutlined,
  TwitterOutlined,
  InstagramOutlined,
  LinkedinOutlined,
} from "@ant-design/icons";
import Logo from "@/components/Logo";

const { Footer: AntFooter } = Layout;
const { Title, Text, Paragraph } = Typography;

// ==============================|| FOOTER ||============================== //

const Footer = () => {
  return (
    <AntFooter
      style={{
        background: "#fff",
        padding: "48px 24px 24px",
        marginTop: "auto",
      }}
    >
      <Row gutter={[32, 32]}>
        <Col xs={24} sm={24} md={6} lg={6}>
          <Space direction="vertical" size={16}>
            <Logo />
            <Paragraph>
              Work Finder is a job search platform connecting job seekers with
              employers across various industries.
            </Paragraph>
            <Space>
              <Link to="#">
                <FacebookOutlined
                  style={{ fontSize: "20px", color: "#1890ff" }}
                />
              </Link>
              <Link to="#">
                <TwitterOutlined
                  style={{ fontSize: "20px", color: "#1890ff" }}
                />
              </Link>
              <Link to="#">
                <InstagramOutlined
                  style={{ fontSize: "20px", color: "#1890ff" }}
                />
              </Link>
              <Link to="#">
                <LinkedinOutlined
                  style={{ fontSize: "20px", color: "#1890ff" }}
                />
              </Link>
            </Space>
          </Space>
        </Col>

        <Col xs={12} sm={12} md={4} lg={4}>
          <Title level={5}>For Candidates</Title>
          <Space direction="vertical" size={8}>
            <Link to="#">Browse Jobs</Link>
            <Link to="#">Browse Categories</Link>
            <Link to="#">Candidate Dashboard</Link>
            <Link to="#">Job Alerts</Link>
            <Link to="#">My Bookmarks</Link>
          </Space>
        </Col>

        <Col xs={12} sm={12} md={4} lg={4}>
          <Title level={5}>For Employers</Title>
          <Space direction="vertical" size={8}>
            <Link to="#">Post a Job</Link>
            <Link to="#">Browse Candidates</Link>
            <Link to="#">Employer Dashboard</Link>
            <Link to="#">Applications</Link>
            <Link to="#">Packages</Link>
          </Space>
        </Col>

        <Col xs={12} sm={12} md={5} lg={5}>
          <Title level={5}>About Us</Title>
          <Space direction="vertical" size={8}>
            <Link to="#">About Company</Link>
            <Link to="#">Contact Us</Link>
            <Link to="#">Our Services</Link>
            <Link to="#">Privacy Policy</Link>
            <Link to="#">Terms & Conditions</Link>
          </Space>
        </Col>

        <Col xs={12} sm={12} md={5} lg={5}>
          <Title level={5}>Helpful Resources</Title>
          <Space direction="vertical" size={8}>
            <Link to="#">Site Map</Link>
            <Link to="#">Terms of Use</Link>
            <Link to="#">Support Center</Link>
            <Link to="#">Career Advice</Link>
            <Link to="#">FAQs</Link>
          </Space>
        </Col>
      </Row>

      <Divider style={{ margin: "32px 0 24px" }} />

      <Row justify="space-between" align="middle">
        <Col>
          <Text type="secondary">
            © {new Date().getFullYear()} Work Finder. All rights reserved.
          </Text>
        </Col>
        <Col>
          <Space size={16}>
            <Link to="#">Privacy Policy</Link>
            <Link to="#">Terms of Service</Link>
            <Link to="#">Cookie Settings</Link>
          </Space>
        </Col>
      </Row>
    </AntFooter>
  );
};

export default Footer;
