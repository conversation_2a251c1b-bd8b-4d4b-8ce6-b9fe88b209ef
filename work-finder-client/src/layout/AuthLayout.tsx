import { Outlet } from "react-router-dom";
import { Card, Layout, Typography } from "antd";

const { Content } = Layout;
const { Title } = Typography;

// ==============================|| AUTH LAYOUT ||============================== //

const AuthLayout = () => {
  return (
    <Layout style={{ minHeight: "100vh" }}>
      <Content
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          background: "#f0f2f5",
        }}
      >
        <Card
          style={{
            width: 400,
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            borderRadius: "8px",
          }}
        >
          <div style={{ textAlign: "center", marginBottom: 24 }}>
            <Title level={3}>Work Finder</Title>
          </div>
          <Outlet />
        </Card>
      </Content>
    </Layout>
  );
};

export default AuthLayout;
