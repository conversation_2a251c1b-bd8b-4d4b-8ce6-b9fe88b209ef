import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Layout, Button, Space, Dropdown, Avatar } from "antd";
import { UserOutlined, BellOutlined, MenuOutlined } from "@ant-design/icons";
import type { MenuProps } from "antd";
import Logo from "@/components/Logo";
import NavbarMenu from "@/layout/MenuList/NavbarMenu";

const { Header } = Layout;

// ==============================|| PUBLIC HEADER ||============================== //

const PublicHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const isHomepage = location.pathname === "/";

  const userItems: MenuProps["items"] = [
    {
      key: "1",
      label: "Profile",
    },
    {
      key: "2",
      label: "Settings",
    },
    {
      key: "3",
      label: "Logout",
    },
  ];

  // Check if user is logged in - this is a placeholder, replace with your auth logic
  const isLoggedIn = false;

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 100); // Change background after scrolling 100px
    };

    if (isHomepage) {
      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
    } else {
      setIsScrolled(true); // Always white background on non-homepage
    }
  }, [isHomepage]);

  return (
    <Header
      style={{
        background:
          isHomepage && !isScrolled
            ? "transparent"
            : "rgba(255, 255, 255, 0.95)",
        backdropFilter: isScrolled ? "blur(10px)" : "none",
        boxShadow: isScrolled ? "0 2px 8px rgba(0,0,0,0.06)" : "none",
        padding: "0 24px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        height: "64px",
        transition: "all 0.3s ease",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flex: 1,
        }}
      >
        <Link to="/">
          <Logo />
        </Link>
        <div
          style={{
            display: "flex",
            marginLeft: 40,
            flex: 1,
          }}
          className="desktop-only"
        >
          <NavbarMenu />
        </div>
      </div>

      <div
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        {isLoggedIn ? (
          <Space size={16}>
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{ fontSize: "18px" }}
            />
            <Dropdown menu={{ items: userItems }} placement="bottomRight">
              <Avatar icon={<UserOutlined />} />
            </Dropdown>
          </Space>
        ) : (
          <Space className="desktop-only" size={16}>
            <Button type="text" onClick={() => navigate("/upload-cv")}>
              Upload your CV
            </Button>
            <Button
              style={{
                backgroundColor: "#f5f5f5",
                color: "#666",
                border: "1px solid #d9d9d9",
              }}
              onClick={() => navigate("/login")}
            >
              Login / Register
            </Button>
            <Button type="primary" onClick={() => navigate("/job-post")}>
              Job Post
            </Button>
          </Space>
        )}

        <Button
          type="text"
          icon={<MenuOutlined />}
          className="mobile-only"
          onClick={() => setMobileMenuVisible(!mobileMenuVisible)}
          style={{ marginLeft: 16 }}
        />
      </div>
    </Header>
  );
};

export default PublicHeader;
