import { memo, useMemo } from "react";

// antd
import { Menu, Typography } from "antd";

// project imports
import menuItem from "../../menu-items";
import { NavItemType } from "@/types/menu";
import { userAuthorization } from "@/utils/authorization";
import NavGroup from "./NavGroup";
import { useAppSelector } from "@/app/hooks";

// ==============================|| SIDEBAR MENU LIST ||============================== //

const MenuList = () => {
  // last menu-item to show in horizontal menu bar
  const auth = useAppSelector((state) => state.auth);
  const { drawerOpen } = useAppSelector((state) => state.menu);

  const navItems = useMemo(() => {
    const lastItem = null;

    let lastItemIndex = menuItem.items.length - 1;
    let remItems: NavItemType[] = [];
    let lastItemId = "";

    if (lastItem && lastItem < menuItem.items.length) {
      lastItemId = menuItem.items[lastItem - 1].id!;
      lastItemIndex = lastItem - 1;
      remItems = menuItem.items
        .slice(lastItem - 1, menuItem.items.length)
        .map((item: NavItemType) => ({
          title: item.title,
          elements: item.children,
        }));
    }

    return menuItem.items
      .slice(0, lastItemIndex + 1)
      .map((item: NavItemType) => {
        const { isAllowFunctions } = userAuthorization(item.access);

        switch (item.type) {
          case "group":
            return (
              (isAllowFunctions || !item.access) && (
                <NavGroup
                  key={item.id}
                  item={item}
                  lastItem={lastItem!}
                  remItems={remItems}
                  lastItemId={lastItemId}
                />
              )
            );
          default:
            return (
              <Typography.Text
                key={item.id}
                type="danger"
                style={{ textAlign: "center", display: "block" }}
              >
                Menu Items Error
              </Typography.Text>
            );
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth]);

  // Render for sidebar mode (wrapped in Menu)
  return (
    <Menu
      mode="inline"
      theme="light"
      inlineCollapsed={!drawerOpen}
      style={{
        height: "100%",
        borderRight: "none",
      }}
    >
      {navItems}
    </Menu>
  );
};

export default memo(MenuList);
