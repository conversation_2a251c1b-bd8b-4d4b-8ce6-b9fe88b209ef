import {
  ForwardRefExoticComponent,
  RefAttributes,
  forwardRef,
  useEffect,
} from "react";
import { Link, useLocation } from "react-router-dom";

// antd
import { Menu, Badge } from "antd";

// project imports
import { activeID, activeItem } from "@/store/slice/menuSlice";
import { useAppSelector, useAppDispatch } from "@/app/hooks";

// assets
import { DotChartOutlined } from "@ant-design/icons";
import { LinkTarget, NavItemType } from "@/types/menu";

// ==============================|| SIDEBAR MENU LIST ITEMS ||============================== //

interface NavItemProps {
  item: NavItemType;
  level: number;
  parentId: string;
}

const NavItem = ({ item, level, parentId }: NavItemProps) => {
  const dispatch = useAppDispatch();
  const { pathname } = useLocation();

  const { selectedItem, drawerOpen } = useAppSelector((state) => state.menu);
  const isSelected = selectedItem.findIndex((id) => id === item.id) > -1;

  const Icon = item?.icon!;
  const itemIcon = item?.icon ? (
    <Icon
      style={{
        fontSize: drawerOpen ? "16px" : "20px",
        color: isSelected ? "#1890ff" : "inherit",
      }}
    />
  ) : (
    <DotChartOutlined
      style={{
        color: isSelected ? "#1890ff" : "inherit",
        fontSize: level > 0 ? "6px" : "8px",
      }}
    />
  );

  let itemTarget: LinkTarget = "_self";
  if (item.target) {
    itemTarget = "_blank";
  }

  let listItemProps: {
    component:
      | ForwardRefExoticComponent<RefAttributes<HTMLAnchorElement>>
      | string;
    href?: string;
    target?: LinkTarget;
  } = {
    component: forwardRef((props, ref) => (
      <Link ref={ref} {...props} to={item.url || "#"} target={itemTarget} />
    )),
  };

  if (item?.external) {
    listItemProps = { component: "a", href: item.url, target: itemTarget };
  }

  const itemHandler = (id: string) => {
    dispatch(activeItem([id]));
    dispatch(activeID(parentId));
  };

  // active menu item on page load
  useEffect(() => {
    const currentIndex = document.location.pathname
      .toString()
      .split("/")
      .findIndex((id) => id === item.id);
    if (currentIndex > -1) {
      dispatch(activeItem([item.id]));
    }
    // eslint-disable-next-line
  }, [pathname]);

  // Render for sidebar mode (original logic)
  return (
    <Menu.Item
      key={item.id}
      icon={itemIcon}
      disabled={item.disabled}
      onClick={() => itemHandler(item.id!)}
      style={{
        paddingLeft: drawerOpen ? level * 24 + 24 : 24,
        fontWeight: isSelected ? 600 : 400,
        color: isSelected ? "#1890ff" : "inherit",
      }}
    >
      <Link to={item.url || "#"} target={itemTarget}>
        {item.title}
      </Link>
      {item.chip && <Badge count={item.chip.label} style={{ marginLeft: 8 }} />}
    </Menu.Item>
  );
};

export default NavItem;
