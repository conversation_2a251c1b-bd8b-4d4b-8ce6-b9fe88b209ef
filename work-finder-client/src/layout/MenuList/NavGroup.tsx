import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

// antd
import { Menu, Typography } from "antd";
import { UpOutlined, DownOutlined } from "@ant-design/icons";

// project imports
import { useAppDispatch, useAppSelector } from "@/app/hooks";
import { activeID } from "@/store/slice/menuSlice";
import { NavItemType } from "@/types/menu";
import { userAuthorization } from "@/utils/authorization";
import NavCollapse from "./NavCollapse";
import NavItem from "./NavItem";

// ==============================|| SIDEBAR MENU LIST GROUP ||============================== //

interface NavGroupProps {
  item: NavItemType;
  lastItem: number;
  remItems: NavItemType[];
  lastItemId: string;
}

const NavGroup = ({ item, lastItem, remItems, lastItemId }: NavGroupProps) => {
  const dispatch = useAppDispatch();
  const { pathname } = useLocation();
  const { drawerOpen } = useAppSelector((state) => state.menu);

  const [currentItem, setCurrentItem] = useState(item);
  const [open, setOpen] = useState(true);

  const handleClick = () => {
    setOpen(!open);
  };

  useEffect(() => {
    if (lastItem) {
      if (item.id === lastItemId) {
        const localItem: any = { ...item };
        const elements = remItems.map((ele: NavItemType) => ele.elements);
        localItem.children = elements.flat(1);
        setCurrentItem(localItem);
      } else {
        setCurrentItem(item);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [item, lastItem]);

  const checkOpenForParent = (child: NavItemType[], id: string) => {
    child.forEach((ele: NavItemType) => {
      if (ele.children?.length) {
        checkOpenForParent(ele.children, currentItem.id!);
      }
      if (ele.url === pathname) {
        dispatch(activeID(id));
      }
    });
  };

  const checkSelectedOnload = (data: NavItemType) => {
    const childrens = data.children ? data.children : [];
    childrens.forEach((itemCheck: NavItemType) => {
      if (itemCheck.children?.length) {
        checkOpenForParent(itemCheck.children, currentItem.id!);
      }
      if (itemCheck.url === pathname) {
        dispatch(activeID(currentItem.id!));
      }
    });
  };

  // keep selected-menu on page load
  useEffect(() => {
    checkSelectedOnload(currentItem);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, currentItem]);

  // menu list collapse & items
  const items = currentItem.children?.map((menu) => {
    const { isAllowFunctions } = userAuthorization(menu.access);
    switch (menu.type) {
      case "collapse":
        return (
          (isAllowFunctions || !menu.access) && (
            <NavCollapse
              key={menu.id}
              menu={menu}
              level={1}
              parentId={currentItem.id!}
            />
          )
        );
      case "item":
        return (
          (isAllowFunctions || !menu.access) && (
            <NavItem
              key={menu.id}
              item={menu}
              level={1}
              parentId={currentItem.id!}
            />
          )
        );
      default:
        return null;
    }
  });

  // Render for sidebar mode (original logic)
  return (
    <Menu.ItemGroup
      key={currentItem.id}
      title={
        currentItem.title &&
        drawerOpen && (
          <div
            onClick={handleClick}
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              cursor: "pointer",
              padding: "8px 0",
            }}
          >
            <Typography.Text
              type="secondary"
              style={{
                fontSize: "12px",
                textTransform: "uppercase",
                fontWeight: 600,
                letterSpacing: "0.5px",
              }}
            >
              {currentItem.title}
              {currentItem.caption && (
                <Typography.Text
                  type="secondary"
                  style={{
                    fontSize: "11px",
                    display: "block",
                    marginTop: "2px",
                  }}
                >
                  {currentItem.caption}
                </Typography.Text>
              )}
            </Typography.Text>
            {open ? (
              <UpOutlined style={{ fontSize: "10px" }} />
            ) : (
              <DownOutlined style={{ fontSize: "10px" }} />
            )}
          </div>
        )
      }
    >
      {open && items}
      {/* group divider */}
      {drawerOpen && <Menu.Divider style={{ margin: "8px 0" }} />}
    </Menu.ItemGroup>
  );
};

export default NavGroup;
