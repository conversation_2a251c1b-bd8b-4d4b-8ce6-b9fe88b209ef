import { memo, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

// antd
import { Menu } from "antd";
import { DownOutlined } from "@ant-design/icons";

// project imports
import publicMenu from "../../menu-items/public-menu";
import { NavItemType } from "@/types/menu";
import { userAuthorization } from "@/utils/authorization";

// ==============================|| NAVBAR MENU LIST ||============================== //

const NavbarMenu = () => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleMenuClick = (url?: string) => {
    if (url) {
      navigate(url);
    }
  };

  const navItems = useMemo(() => {
    return publicMenu.items.map((item: NavItemType) => {
      const { isAllowFunctions } = userAuthorization(item.access);
      const hasChildren = item.children && item.children.length > 0;

      // Render menu items directly for navbar (no groups)
      switch (item.type) {
        case "collapse":
          return (
            (isAllowFunctions || !item.access) && (
              <Menu.SubMenu
                key={item.id}
                title={
                  <span
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: hasChildren ? 8 : 0,
                      cursor: "pointer",
                    }}
                    onMouseEnter={() => setHoveredItem(item.id || null)}
                    onMouseLeave={() => setHoveredItem(null)}
                    onClick={() => handleMenuClick(item.url)}
                  >
                    {item.title}
                    {hasChildren && (
                      <DownOutlined
                        style={{
                          fontSize: "12px",
                          transition: "transform 0.3s ease",
                          transform:
                            hoveredItem === item.id
                              ? "rotate(180deg)"
                              : "rotate(0deg)",
                        }}
                      />
                    )}
                  </span>
                }
                popupOffset={[0, 8]}
              >
                {hasChildren &&
                  item.children?.map((child) => {
                    const { isAllowFunctions: childAllowed } =
                      userAuthorization(child.access);
                    return (
                      (childAllowed || !child.access) && (
                        <Menu.Item
                          key={child.id}
                          onClick={() => handleMenuClick(child.url)}
                        >
                          {child.title}
                        </Menu.Item>
                      )
                    );
                  })}
              </Menu.SubMenu>
            )
          );
        case "item":
          return (
            (isAllowFunctions || !item.access) && (
              <Menu.Item
                key={item.id}
                onClick={() => handleMenuClick(item.url)}
                style={{ cursor: "pointer" }}
              >
                {item.title}
              </Menu.Item>
            )
          );
        default:
          return null;
      }
    });
  }, [hoveredItem, navigate]);

  // Custom styles using CSS-in-JS
  const menuStyles = {
    borderBottom: "none",
    backgroundColor: "transparent",
    flex: 1,
  };

  const customMenuStyle = `
    .custom-navbar-menu.ant-menu-horizontal > .ant-menu-item:hover,
    .custom-navbar-menu.ant-menu-horizontal > .ant-menu-submenu:hover > .ant-menu-submenu-title {
      color: #1890ff !important;
      background: transparent !important;
    }
    .custom-navbar-menu.ant-menu-horizontal > .ant-menu-item::after,
    .custom-navbar-menu.ant-menu-horizontal > .ant-menu-submenu::after {
      border-bottom: none !important;
    }
  `;

  return (
    <>
      <style>{customMenuStyle}</style>
      <Menu
        mode="horizontal"
        theme="light"
        style={menuStyles}
        expandIcon={null}
        triggerSubMenuAction="hover"
        className="custom-navbar-menu"
      >
        {navItems}
      </Menu>
    </>
  );
};

export default memo(NavbarMenu);
