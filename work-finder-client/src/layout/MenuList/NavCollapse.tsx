import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

// antd
import { Menu } from "antd";

// project imports
import NavItem from "./NavItem";
import { useAppSelector } from "@/app/hooks";
import { NavItemType } from "@/types/menu";
import { userAuthorization } from "@/utils/authorization";

// ==============================|| SIDEBAR MENU LIST COLLAPSE ITEMS ||============================== //

interface NavCollapseProps {
  menu: NavItemType;
  level: number;
  parentId: string;
}

const NavCollapse = ({ menu, level, parentId }: NavCollapseProps) => {
  const { drawerOpen } = useAppSelector((state) => state.menu);

  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string | null | undefined>(null);

  const handleClick = () => {
    setOpen(!open);
    setSelected(!selected ? menu.id : null);
  };

  const { pathname } = useLocation();

  const checkOpenForParent = (child: NavItemType[], id: string) => {
    child.forEach((item: NavItemType) => {
      if (item.url === pathname) {
        setOpen(true);
        setSelected(id);
      }
    });
  };

  // menu collapse for sub-levels
  useEffect(() => {
    setOpen(false);
    setSelected(null);
    if (menu.children) {
      menu.children.forEach((item: NavItemType) => {
        if (item.children?.length) {
          checkOpenForParent(item.children, menu.id!);
        }
        if (item.url === pathname) {
          setSelected(menu.id);
          setOpen(true);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, menu.children]);

  // menu collapse & item
  const menus = menu.children?.map((item) => {
    const { isAllowFunctions } = userAuthorization(item.access);
    switch (item.type) {
      case "collapse":
        return (
          (isAllowFunctions || !item.access) && (
            <NavCollapse
              key={item.id}
              menu={item}
              level={level + 1}
              parentId={parentId}
            />
          )
        );
      case "item":
        return (
          (isAllowFunctions || !item.access) && (
            <NavItem
              key={item.id}
              item={item}
              level={level + 1}
              parentId={parentId}
            />
          )
        );
      default:
        return null;
    }
  });

  const isSelected = selected === menu.id;
  const Icon = menu.icon!;
  const menuIcon = menu.icon ? (
    <Icon
      style={{
        fontSize: drawerOpen ? "16px" : "20px",
        color: isSelected ? "#1890ff" : "inherit",
      }}
    />
  ) : null;

  // Render for sidebar mode (original logic)
  return (
    <Menu.SubMenu
      key={menu.id}
      icon={menuIcon}
      title={menu.title}
      onTitleClick={handleClick}
      style={{
        paddingLeft: drawerOpen ? level * 24 : 24,
      }}
    >
      {menus}
    </Menu.SubMenu>
  );
};

export default NavCollapse;
