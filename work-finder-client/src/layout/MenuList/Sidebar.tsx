import { memo } from "react";

// antd
import { Layout, But<PERSON>, theme } from "antd";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";

// project imports
import MenuList from "./index";
import { useAppSelector, useAppDispatch } from "@/app/hooks";
import { openDrawer } from "@/store/slice/menuSlice";

const { Sider } = Layout;

// ==============================|| SIDEBAR DRAWER ||============================== //

const Sidebar = () => {
  const antdTheme = theme.useToken();
  const dispatch = useAppDispatch();
  const { drawerOpen } = useAppSelector((state) => state.menu);

  const toggleDrawer = () => {
    dispatch(openDrawer(!drawerOpen));
  };

  const siderWidth = drawerOpen ? 280 : 80;

  return (
    <Sider
      width={siderWidth}
      collapsed={!drawerOpen}
      onCollapse={toggleDrawer}
      style={{
        background: antdTheme.token.colorBgContainer,
        borderRight: `1px solid ${antdTheme.token.colorBorder}`,
      }}
      trigger={null}
    >
      {/* Header with logo and toggle */}
      <div
        style={{
          height: 64,
          display: "flex",
          alignItems: "center",
          justifyContent: drawerOpen ? "space-between" : "center",
          padding: drawerOpen ? "0 16px" : "0 8px",
          borderBottom: `1px solid ${antdTheme.token.colorBorder}`,
        }}
      >
        {drawerOpen && (
          <div
            style={{
              fontWeight: "bold",
              fontSize: "16px",
              color: antdTheme.token.colorPrimary,
            }}
          >
            Work Finder
          </div>
        )}

        <Button
          type="text"
          icon={drawerOpen ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
          onClick={toggleDrawer}
          title={drawerOpen ? "Collapse" : "Expand"}
          size="small"
        />
      </div>

      {/* Menu content */}
      <div
        style={{
          height: "calc(100vh - 64px)",
          overflow: "auto",
          padding: "8px 0",
        }}
      >
        <MenuList />
      </div>
    </Sider>
  );
};

export default memo(Sidebar);
