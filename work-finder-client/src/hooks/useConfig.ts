import { useState } from "react";

// Default values
const defaultConfig = {
  fontFamily: "'Roboto', sans-serif",
  borderRadius: 8,
  outlinedFilled: true,
  navType: "light" as "light" | "dark",
  presetColor: "default",
  locale: "en",
};

/**
 * Hook to manage theme configuration
 */
const useConfig = () => {
  const [config, setConfig] = useState({
    ...defaultConfig,
  });

  const onChangeMenuType = (navType: "light" | "dark") => {
    setConfig({
      ...config,
      navType,
    });
  };

  const onChangePresetColor = (presetColor: string) => {
    setConfig({
      ...config,
      presetColor,
    });
  };

  const onChangeFontFamily = (fontFamily: string) => {
    setConfig({
      ...config,
      fontFamily,
    });
  };

  const onChangeBorderRadius = (borderRadius: number) => {
    setConfig({
      ...config,
      borderRadius,
    });
  };

  const onChangeOutlinedField = (outlinedFilled: boolean) => {
    setConfig({
      ...config,
      outlinedFilled,
    });
  };

  return {
    ...config,
    onChangeMenuType,
    onChangePresetColor,
    onChangeFontFamily,
    onChangeBorderRadius,
    onChangeOutlinedField,
  };
};

export default useConfig;
