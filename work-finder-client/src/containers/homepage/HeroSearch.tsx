import { <PERSON><PERSON>, <PERSON>, Col } from "antd";
import { useState } from "react";
import { JobTitle, Location } from "@/containers";

// ==============================|| HERO SEARCH CONTAINER ||============================== //

interface HeroSearchProps {
  onSearch?: (jobTitle: string, location: string) => void;
  defaultValues?: {
    jobTitle?: string;
    location?: string;
  };
  style?: React.CSSProperties;
}

const HeroSearch = ({
  onSearch,
  defaultValues = {},
  style = {},
}: HeroSearchProps) => {
  const [jobTitle, setJobTitle] = useState(defaultValues.jobTitle || "");
  const [location, setLocation] = useState(defaultValues.location || "");

  const handleSearch = () => {
    onSearch?.(jobTitle, location);
  };

  const handleJobTitleChange = (value: string) => {
    setJobTitle(value);
  };

  const handleLocationChange = (value: string) => {
    setLocation(value);
  };

  return (
    <div
      style={{
        background: "white",
        borderRadius: "12px",
        padding: "8px",
        boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)",
        ...style,
      }}
    >
      <Row gutter={0}>
        <Col flex="1">
          <JobTitle
            name="jobTitle"
            value={jobTitle}
            onChange={handleJobTitleChange}
            onPressEnter={handleSearch}
            style={{
              border: "none",
              boxShadow: "none",
              fontSize: "16px",
              backgroundColor: "transparent",
            }}
          />
        </Col>
        <Col flex="1">
          <Location
            name="location"
            value={location}
            onChange={handleLocationChange}
            onPressEnter={handleSearch}
            style={{
              border: "none",
              borderLeft: "1px solid #f0f0f0",
              boxShadow: "none",
              fontSize: "16px",
              backgroundColor: "transparent",
            }}
          />
        </Col>
        <Col>
          <Button
            type="primary"
            size="large"
            onClick={handleSearch}
            style={{
              background: "#4F46E5",
              borderColor: "#4F46E5",
              borderRadius: "8px",
              fontWeight: "600",
              minWidth: "120px",
            }}
          >
            Find Jobs
          </Button>
        </Col>
      </Row>
    </div>
  );
};

export default HeroSearch;
