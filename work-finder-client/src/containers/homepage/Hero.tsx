import { Typography, Row, Col, Space, Tag } from "antd";
import { HeroSearch } from "@/containers";
import { popularSearches } from "@/pages/homepage/Config";
import FloatingCard from "@/components/cards/FloatingCard";
import heroRightImage from "@/assets/images/hero_right.png";

const { Title, Text } = Typography;

// ==============================|| HERO CONTAINER ||============================== //

interface HeroProps {
  onSearch?: (jobTitle: string, location: string) => void;
}

const Hero = ({ onSearch }: HeroProps) => {
  const handleSearch = (jobTitle: string, location: string) => {
    onSearch?.(jobTitle, location);
  };

  return (
    <div
      style={{
        background: "linear-gradient(135deg, #f7f8fc 0%, #e7eef9 100%)",
        minHeight: "80vh",
        paddingTop: "0", // No padding since background covers header
        paddingBottom: "40px",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "0 24px",
          height: "100%",
        }}
      >
        <Row
          style={{
            height: "100%",
            minHeight: "80vh",
            paddingTop: "64px", // Updated to match header height
          }}
          align="middle"
          gutter={[48, 32]}
        >
          {/* Left Column - Content */}
          <Col xs={{ span: 24 }} lg={{ span: 12, order: 1 }}>
            <div style={{ textAlign: "left" }} className="hero-content-mobile">
              <Title
                level={1}
                style={{
                  color: "#1e293b",
                  fontSize: "clamp(32px, 5vw, 48px)",
                  fontWeight: "bold",
                  marginBottom: "8px",
                  lineHeight: "1.2",
                }}
              >
                There Are <span style={{ color: "#3b82f6" }}>93,178</span>{" "}
                Postings Here
              </Title>
              <Title
                level={1}
                style={{
                  color: "#1e293b",
                  fontSize: "clamp(32px, 5vw, 48px)",
                  fontWeight: "bold",
                  marginBottom: "16px",
                  lineHeight: "1.2",
                }}
              >
                For you!
              </Title>
              <Text
                style={{
                  color: "#64748b",
                  fontSize: "18px",
                  display: "block",
                  marginBottom: "32px",
                }}
              >
                Find Jobs, Employment & Career Opportunities
              </Text>

              {/* Search Form */}
              <HeroSearch
                onSearch={handleSearch}
                style={{ marginBottom: "24px" }}
              />

              {/* Popular Searches */}
              <div style={{ textAlign: "left" }}>
                <Text
                  style={{
                    color: "#64748b",
                    fontSize: "14px",
                    marginRight: "12px",
                  }}
                >
                  Popular Searches :
                </Text>
                <Space wrap>
                  {popularSearches.map((search) => (
                    <Tag
                      key={search}
                      style={{
                        background: "rgba(59, 130, 246, 0.1)",
                        color: "#3b82f6",
                        border: "1px solid rgba(59, 130, 246, 0.3)",
                        borderRadius: "16px",
                        padding: "4px 12px",
                        cursor: "pointer",
                      }}
                      onClick={() => handleSearch(search, "")}
                    >
                      {search}
                    </Tag>
                  ))}
                </Space>
              </div>
            </div>
          </Col>

          {/* Right Column - Hero Image */}
          <Col
            xs={{ span: 0 }}
            lg={{ span: 12, order: 2 }}
            className="hero-image-col"
          >
            <div
              style={{
                position: "relative",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                minHeight: "500px",
              }}
            >
              <img
                src={heroRightImage}
                alt="WorkFinder Hero"
                style={{
                  width: "100%",
                  maxWidth: "500px",
                  height: "auto",
                  objectFit: "contain",
                }}
              />

              {/* Floating Cards - Minimal and positioned to not block content */}
              <div className="hidden-mobile">
                <FloatingCard
                  title="Work Inquiry From"
                  subtitle="WorkFinder"
                  dotColor="#FFA500"
                  style={{
                    position: "absolute",
                    top: "10%",
                    left: "-30%",
                    transform: "rotate(-8deg)",
                    zIndex: 10,
                    maxWidth: "200px",
                  }}
                />

                <FloatingCard
                  title="Creative Agency"
                  subtitle="Startup"
                  dotColor="#FF6B6B"
                  style={{
                    position: "absolute",
                    bottom: "15%",
                    right: "-20%",
                    transform: "rotate(8deg)",
                    zIndex: 10,
                    maxWidth: "180px",
                  }}
                />

                {/* Candidates indicator - repositioned */}
                <div
                  style={{
                    position: "absolute",
                    top: "15%",
                    right: "-10%",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    background: "rgba(255, 255, 255, 0.95)",
                    padding: "12px 16px",
                    borderRadius: "25px",
                    boxShadow: "0 8px 20px rgba(0, 0, 0, 0.08)",
                  }}
                >
                  <Text
                    style={{
                      color: "#64748b",
                      fontSize: "13px",
                      fontWeight: "600",
                    }}
                  >
                    10k+ Candidates
                  </Text>
                  <div style={{ display: "flex", marginLeft: "8px" }}>
                    {[1, 2, 3, 4].map((i) => (
                      <div
                        key={i}
                        style={{
                          width: "20px",
                          height: "20px",
                          borderRadius: "50%",
                          background: "#e2e8f0",
                          marginLeft: i > 1 ? "-4px" : "0",
                          border: "2px solid white",
                        }}
                      />
                    ))}
                    <div
                      style={{
                        width: "20px",
                        height: "20px",
                        borderRadius: "50%",
                        background: "#3b82f6",
                        marginLeft: "-4px",
                        border: "2px solid white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: "white",
                        fontSize: "10px",
                        fontWeight: "bold",
                      }}
                    >
                      +
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Hero;
