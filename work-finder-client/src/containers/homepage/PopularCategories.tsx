import React from "react";
import { Row, Col, Typography } from "antd";
import {
  IconCoin,
  IconSpeakerphone,
  IconPaint,
  IconCode,
  IconUsers,
  IconRocket,
  IconUserCheck,
  IconHeart,
  IconCar,
} from "@tabler/icons-react";
import CategoryCard from "./CategoryCard";

const { Title } = Typography;

interface CategoryData {
  id: string;
  title: string;
  jobCount: number;
  icon: React.ComponentType<any>;
}

const categoriesData: CategoryData[] = [
  {
    id: "accounting-finance",
    title: "Accounting / Finance",
    jobCount: 2,
    icon: IconCoin,
  },
  {
    id: "marketing",
    title: "Marketing",
    jobCount: 86,
    icon: IconSpeakerphone,
  },
  {
    id: "design",
    title: "Design",
    jobCount: 43,
    icon: IconPaint,
  },
  {
    id: "development",
    title: "Development",
    jobCount: 12,
    icon: IconCode,
  },
  {
    id: "human-resource",
    title: "Human Resource",
    jobCount: 55,
    icon: IconUsers,
  },
  {
    id: "project-management",
    title: "Project Management",
    jobCount: 19,
    icon: IconRocket,
  },
  {
    id: "customer-service",
    title: "Customer Service",
    jobCount: 72,
    icon: IconUserCheck,
  },
  {
    id: "health-care",
    title: "Health and Care",
    jobCount: 26,
    icon: IconHeart,
  },
  {
    id: "automotive",
    title: "Automotive Jobs",
    jobCount: 92,
    icon: IconCar,
  },
];

interface PopularCategoriesProps {
  onCategoryClick?: (categoryId: string) => void;
}

const PopularCategories: React.FC<PopularCategoriesProps> = ({
  onCategoryClick,
}) => {
  const handleCategoryClick = (categoryId: string) => {
    if (onCategoryClick) {
      onCategoryClick(categoryId);
    }
  };

  return (
    <div
      style={{
        padding: "32px 0",
        maxWidth: "1200px",
        margin: "0 auto",
        paddingLeft: "24px",
        paddingRight: "24px",
      }}
    >
      <div style={{ textAlign: "center", marginBottom: "48px" }}>
        <Title
          level={2}
          style={{
            margin: 0,
            marginBottom: "8px",
            fontSize: "32px",
            fontWeight: 600,
            color: "#262626",
          }}
        >
          Popular Job Categories
        </Title>
        <p
          style={{
            color: "#8c8c8c",
            fontSize: "16px",
            margin: 0,
            lineHeight: "24px",
          }}
        >
          2020 jobs live • 293 added today.
        </p>
      </div>

      <Row gutter={[24, 24]}>
        {categoriesData.map((category) => (
          <Col xs={24} sm={12} lg={8} key={category.id}>
            <CategoryCard
              icon={category.icon}
              title={category.title}
              jobCount={category.jobCount}
              onClick={() => handleCategoryClick(category.id)}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PopularCategories;
