import React, { useState } from "react";
import { Card, Typography } from "antd";

const { Title, Text } = Typography;

interface CategoryCardProps {
  icon: React.ComponentType<any>;
  title: string;
  jobCount: number;
  onClick?: () => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  icon: Icon,
  title,
  jobCount,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      hoverable
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        borderRadius: "8px",
        cursor: "pointer",
        transition: "all 0.3s ease",
        border: "1px solid #f0f0f0",
        height: "80px",
      }}
      bodyStyle={{
        padding: "16px",
        display: "flex",
        alignItems: "center",
        height: "100%",
      }}
    >
      <div
        style={{
          width: "48px",
          height: "48px",
          borderRadius: "8px",
          backgroundColor: isHovered ? "#1890ff" : "#f0f6ff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          marginRight: "16px",
          flexShrink: 0,
          transition: "all 0.3s ease",
        }}
      >
        <Icon size={24} color={isHovered ? "#ffffff" : "#1890ff"} />
      </div>

      <div style={{ flex: 1, minWidth: 0 }}>
        <Title
          level={5}
          style={{
            margin: 0,
            fontSize: "15px",
            fontWeight: 600,
            lineHeight: "20px",
            marginBottom: "4px",
            color: isHovered ? "#1890ff" : "#262626",
            transition: "all 0.3s ease",
          }}
        >
          {title}
        </Title>
        <Text
          type="secondary"
          style={{
            fontSize: "13px",
            color: "#8c8c8c",
            lineHeight: "16px",
          }}
        >
          ({jobCount} open positions)
        </Text>
      </div>
    </Card>
  );
};

export default CategoryCard;
