import { Input } from "antd";
import { EnvironmentOutlined } from "@ant-design/icons";
import { ReactNode } from "react";

// ==============================|| LOCATION SEARCH FIELD ||============================== //

interface LocationProps {
  name: string;
  label?: string | ReactNode;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onPressEnter?: () => void;
  disabled?: boolean;
  required?: boolean;
  size?: "small" | "middle" | "large";
  style?: React.CSSProperties;
}

const Location = ({
  name,
  label,
  placeholder = "City or postcode",
  value,
  onChange,
  onPressEnter,
  disabled = false,
  required = false,
  size = "large",
  style = {},
}: LocationProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div>
      {label && (
        <label
          style={{ display: "block", marginBottom: "4px", fontWeight: "500" }}
        >
          {label}
          {required && <span style={{ color: "red" }}> *</span>}
        </label>
      )}
      <Input
        name={name}
        size={size}
        placeholder={placeholder}
        prefix={<EnvironmentOutlined style={{ color: "#bfbfbf" }} />}
        value={value}
        onChange={handleChange}
        onPressEnter={onPressEnter}
        disabled={disabled}
        style={{
          fontSize: "16px",
          ...style,
        }}
      />
    </div>
  );
};

Location.defaultProps = {
  name: "location",
  placeholder: "City or postcode",
};

export default Location;
