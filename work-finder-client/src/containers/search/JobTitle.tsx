import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { ReactNode } from "react";

// ==============================|| JOB TITLE SEARCH FIELD ||============================== //

interface JobTitleProps {
  name: string;
  label?: string | ReactNode;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onPressEnter?: () => void;
  disabled?: boolean;
  required?: boolean;
  size?: "small" | "middle" | "large";
  style?: React.CSSProperties;
}

const JobTitle = ({
  name,
  label,
  placeholder = "Job title, keywords, or company",
  value,
  onChange,
  onPressEnter,
  disabled = false,
  required = false,
  size = "large",
  style = {},
}: JobTitleProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div>
      {label && (
        <label
          style={{ display: "block", marginBottom: "4px", fontWeight: "500" }}
        >
          {label}
          {required && <span style={{ color: "red" }}> *</span>}
        </label>
      )}
      <Input
        name={name}
        size={size}
        placeholder={placeholder}
        prefix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
        value={value}
        onChange={handleChange}
        onPressEnter={onPressEnter}
        disabled={disabled}
        style={{
          fontSize: "16px",
          ...style,
        }}
      />
    </div>
  );
};

JobTitle.defaultProps = {
  name: "jobTitle",
  placeholder: "Job title, keywords, or company",
};

export default JobTitle;
